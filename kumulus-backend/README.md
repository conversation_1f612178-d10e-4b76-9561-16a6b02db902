# Ku<PERSON>lus Backend

First handle Auth schema

## Install and configure Drizzle

```sh
deno install npm:drizzle-orm npm:drizzle-kit npm:pg npm:@types/pg npm:better-auth
``` 

## Check
https://www.better-auth.com/docs/adapters/drizzle

Create utils folder and under it, add `auth.ts` file with this content

## Generate Auth Tables Deno  BetterAuth Drizzle

```sh
# Generate Schema
deno run -A --node-modules-dir  npm:@better-auth/cli generate
```
The `auth-shcema.ts` file is generated and then we need to push it to the database 
to create the BetterAuth related tables.
We need to make sure that `drizzle.config.ts` exist and is configure and that it points to `auth-schema.ts`
file at this stage and then later we can point it back to `schema.ts` that might be generated from subsequent drizzle pull.

```sh
deno --env -A --node-modules-dir npm:drizzle-kit push
```

After either merge any other related tables and push again or or make changes in the database and do the drizzle pull

## Pulling a postgres database via Drizzle

```bash
### Pull
deno --env -A --node-modules-dir npm:drizzle-kit pull
```

## Running the app

With Postgres as a database

```bash
deno run --env -A app.ts
```