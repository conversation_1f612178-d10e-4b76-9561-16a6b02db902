import { Context, Next } from "hono";
import { auth } from "../utils/auth.ts";

export async function authMiddleware(c: Context, next: Next) {
  try {
    // Get the session from the request
    const session = await auth.api.getSession({
      headers: c.req.header(),
    });

    if (!session) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    // Add session to context
    c.set("session", session);
    c.set("user", session.user);
    
    await next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    return c.json({ error: "Unauthorized" }, 401);
  }
}

export async function optionalAuthMiddleware(c: Context, next: Next) {
  try {
    // Get the session from the request (optional)
    const session = await auth.api.getSession({
      headers: c.req.header(),
    });

    if (session) {
      c.set("session", session);
      c.set("user", session.user);
    }
    
    await next();
  } catch (error) {
    // Continue without auth if there's an error
    await next();
  }
}
