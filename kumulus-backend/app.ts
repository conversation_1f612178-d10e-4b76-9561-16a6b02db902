import { Hono } from "hono";
import type { AuthType } from "./utils/auth.ts";
import auth from "./routes/auth.ts";
import dashboard from "./routes/dashboard.ts";
import profile from "./routes/profile.ts";
import providers from "./routes/providers.ts";
import { cors } from "hono/cors";

const app = new Hono<{ Variables: AuthType }>({
  strict: false,
});

app.use(cors({
  origin: "http://localhost:3000",
  allowHeaders: ["Content-Type", "Authorization", "Cookie"],
  allowMethods: ["POST", "GET", "PUT", "DELETE", "OPTIONS"],
  exposeHeaders: ["Content-Length"],
  maxAge: 600,
  credentials: true,
}));

// Mount routes
app.basePath("/api").route("/", auth);
app.basePath("/api").route("/dashboard", dashboard);
app.basePath("/api").route("/profile", profile);
app.basePath("/api").route("/providers", providers);

Deno.serve(app.fetch);
