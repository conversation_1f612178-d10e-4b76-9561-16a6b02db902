import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";

const router = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
router.use("*", authMiddleware);

// Get all providers for the user
router.get("/", async (c) => {
  const user = c.get("user");
  
  return c.json({
    message: "User providers",
    userId: user.id,
    providers: [
      {
        id: "1",
        name: "AWS S3",
        type: "storage",
        status: "connected",
        lastSync: "2024-01-15T14:30:00Z",
        config: {
          region: "us-east-1",
          bucket: "my-kumulus-bucket",
        },
        stats: {
          totalFiles: 1247,
          totalSize: "2.3 GB",
          lastActivity: "2024-01-15T14:30:00Z",
        },
      },
      {
        id: "2",
        name: "Google Drive",
        type: "storage",
        status: "connected",
        lastSync: "2024-01-15T13:45:00Z",
        config: {
          folderId: "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        },
        stats: {
          totalFiles: 856,
          totalSize: "1.8 GB",
          lastActivity: "2024-01-15T13:45:00Z",
        },
      },
      {
        id: "3",
        name: "Dropbox",
        type: "storage",
        status: "error",
        lastSync: "2024-01-14T09:15:00Z",
        error: "Authentication expired",
        config: {
          appKey: "your-app-key",
        },
        stats: {
          totalFiles: 423,
          totalSize: "890 MB",
          lastActivity: "2024-01-14T09:15:00Z",
        },
      },
    ],
  });
});

// Get specific provider details
router.get("/:id", async (c) => {
  const user = c.get("user");
  const providerId = c.req.param("id");
  
  return c.json({
    message: "Provider details",
    userId: user.id,
    provider: {
      id: providerId,
      name: "AWS S3",
      type: "storage",
      status: "connected",
      lastSync: "2024-01-15T14:30:00Z",
      config: {
        region: "us-east-1",
        bucket: "my-kumulus-bucket",
        accessKeyId: "AKIA***",
      },
      stats: {
        totalFiles: 1247,
        totalSize: "2.3 GB",
        lastActivity: "2024-01-15T14:30:00Z",
        bandwidth: {
          upload: "45.2 MB",
          download: "123.8 MB",
        },
      },
      recentFiles: [
        {
          name: "document.pdf",
          size: "2.3 MB",
          uploadedAt: "2024-01-15T14:30:00Z",
        },
        {
          name: "image.jpg",
          size: "1.8 MB",
          uploadedAt: "2024-01-15T13:45:00Z",
        },
      ],
    },
  });
});

// Add new provider
router.post("/", async (c) => {
  const user = c.get("user");
  const body = await c.req.json();
  
  return c.json({
    message: "Provider added successfully",
    userId: user.id,
    provider: {
      id: Math.random().toString(36).substr(2, 9),
      name: body.name,
      type: body.type,
      status: "pending",
      config: body.config,
      createdAt: new Date().toISOString(),
    },
  });
});

// Update provider configuration
router.put("/:id", async (c) => {
  const user = c.get("user");
  const providerId = c.req.param("id");
  const body = await c.req.json();
  
  return c.json({
    message: "Provider updated successfully",
    userId: user.id,
    provider: {
      id: providerId,
      name: body.name,
      type: body.type,
      status: body.status || "connected",
      config: body.config,
      updatedAt: new Date().toISOString(),
    },
  });
});

// Delete provider
router.delete("/:id", async (c) => {
  const user = c.get("user");
  const providerId = c.req.param("id");
  
  return c.json({
    message: "Provider deleted successfully",
    userId: user.id,
    providerId: providerId,
  });
});

export default router;
