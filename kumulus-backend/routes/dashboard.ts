import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";

const router = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
router.use("*", authMiddleware);

// Dashboard overview endpoint
router.get("/overview", async (c) => {
  const user = c.get("user");
  
  return c.json({
    message: "Dashboard overview",
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
    },
    stats: {
      totalProviders: 5,
      activeConnections: 12,
      totalRequests: 1247,
      uptime: "99.9%",
    },
    recentActivity: [
      {
        id: 1,
        action: "Provider connected",
        provider: "AWS S3",
        timestamp: new Date().toISOString(),
      },
      {
        id: 2,
        action: "File uploaded",
        provider: "Google Drive",
        timestamp: new Date(Date.now() - 3600000).toISOString(),
      },
      {
        id: 3,
        action: "Backup completed",
        provider: "Dropbox",
        timestamp: new Date(Date.now() - 7200000).toISOString(),
      },
    ],
  });
});

// Dashboard analytics endpoint
router.get("/analytics", async (c) => {
  const user = c.get("user");
  
  return c.json({
    message: "Dashboard analytics",
    userId: user.id,
    analytics: {
      requestsOverTime: [
        { date: "2024-01-01", requests: 45 },
        { date: "2024-01-02", requests: 67 },
        { date: "2024-01-03", requests: 89 },
        { date: "2024-01-04", requests: 123 },
        { date: "2024-01-05", requests: 156 },
      ],
      providerUsage: [
        { provider: "AWS S3", usage: 45 },
        { provider: "Google Drive", usage: 32 },
        { provider: "Dropbox", usage: 23 },
      ],
      errorRates: {
        total: 1247,
        errors: 12,
        rate: "0.96%",
      },
    },
  });
});

export default router;
