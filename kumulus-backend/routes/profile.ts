import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";

const router = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
router.use("*", authMiddleware);

// Get user profile
router.get("/", async (c) => {
  const user = c.get("user");
  
  return c.json({
    message: "User profile",
    profile: {
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      image: user.image,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      preferences: {
        theme: "dark",
        notifications: true,
        language: "en",
      },
      subscription: {
        plan: "Pro",
        status: "active",
        expiresAt: "2024-12-31T23:59:59Z",
      },
    },
  });
});

// Update user profile
router.put("/", async (c) => {
  const user = c.get("user");
  const body = await c.req.json();
  
  // In a real app, you would validate and update the user in the database
  return c.json({
    message: "Profile updated successfully",
    profile: {
      id: user.id,
      name: body.name || user.name,
      email: user.email, // Email shouldn't be changed here
      emailVerified: user.emailVerified,
      image: body.image || user.image,
      updatedAt: new Date().toISOString(),
    },
  });
});

// Get user settings
router.get("/settings", async (c) => {
  const user = c.get("user");
  
  return c.json({
    message: "User settings",
    userId: user.id,
    settings: {
      notifications: {
        email: true,
        push: false,
        sms: false,
      },
      privacy: {
        profileVisible: true,
        activityVisible: false,
      },
      security: {
        twoFactorEnabled: false,
        lastPasswordChange: "2024-01-15T10:30:00Z",
      },
      preferences: {
        theme: "dark",
        language: "en",
        timezone: "UTC",
      },
    },
  });
});

// Update user settings
router.put("/settings", async (c) => {
  const user = c.get("user");
  const body = await c.req.json();
  
  return c.json({
    message: "Settings updated successfully",
    userId: user.id,
    settings: body, // In a real app, validate and save to database
  });
});

export default router;
