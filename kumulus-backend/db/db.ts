import { drizzle } from "drizzle-orm/node-postgres";
import pg from "pg";
import * as schema from "../auth-schema.ts";
const { Pool } = pg;

class Database {
  private static instance: Database;
  private db: ReturnType<typeof drizzle>;

  private constructor() {
    const pool = new Pool({
      connectionString: Deno.env.get("DATABASE_URL"),
    });

    this.db = drizzle(pool, {
      schema,
    });
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  public getConnection() {
    return this.db;
  }
}

export const db = Database.getInstance().getConnection();