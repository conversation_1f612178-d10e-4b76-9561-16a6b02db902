# Kumulus Provider Integration Flow

## 🔄 Complete Integration Architecture

### **1. Provider Console Frontend → Backend → Agent Installation**

```mermaid
sequenceDiagram
    participant PC as Provider Console
    participant BE as Backend API
    participant VM as Provider Machine
    participant AG as Kumulus Agent

    Note over PC,AG: Step 1: Resource Registration
    PC->>BE: POST /api/resources (resource specs)
    BE->>BE: Create resource record (status: pending)
    BE->>PC: Return resource_id + install_command
    PC->>PC: Show Step 2: Installation Instructions

    Note over PC,AG: Step 2: Agent Installation
    VM->>VM: curl https://kumulus.cloud/install.sh | sh
    VM->>BE: GET /install.sh?resource_id=xxx&token=yyy
    BE->>VM: Return enhanced provider-setup.sh
    VM->>VM: Execute installation script
    
    Note over PC,AG: Step 2a: Installation Progress
    VM->>BE: POST /api/providers/{id}/installation (progress updates)
    BE->>PC: WebSocket/SSE progress updates
    PC->>PC: Update Step 2 progress bar
    
    Note over PC,AG: Step 2b: Agent Startup
    VM->>VM: systemctl start kumulus-agent
    AG->>BE: POST /api/providers/register (system_info + signature)
    BE->>BE: Update resource status: installing → ready
    BE->>PC: WebSocket: status change notification
    PC->>PC: Show Step 3: Ready to Provide

    Note over PC,AG: Step 3: Start Providing
    PC->>BE: POST /api/resources/{id}/start
    BE->>AG: Send activation signal
    AG->>BE: Confirm activation + start health checks
    BE->>BE: Update status: ready → active
    BE->>PC: Redirect to resources dashboard
```

### **2. Enhanced Installation Script Integration**

#### **Current Script Issues Fixed:**
- ❌ **No Progress Reporting** → ✅ **Real-time progress updates to console**
- ❌ **No Provider Context** → ✅ **Uses PROVIDER_ID, RESOURCE_ID, tokens**
- ❌ **Basic Installation** → ✅ **Production-ready with systemd service**
- ❌ **No Security** → ✅ **Secure key management and permissions**

#### **Enhanced Script Features:**
```bash
# The script now includes:
curl -fsSL "https://kumulus.cloud/install.sh?resource_id=${RESOURCE_ID}&token=${TOKEN}" | sh

# Which provides:
- System requirements checking
- Secure user and directory setup
- Progress reporting to provider console
- Systemd service creation
- Automatic startup and monitoring
- Integration with provider dashboard
```

### **3. Provider Console Updates Needed**

#### **Step 2 Enhancement (Installation Instructions):**

```typescript
// In the Step 2 component, add real-time progress tracking:

const [installationProgress, setInstallationProgress] = useState({
  step: '',
  status: 'pending',
  progress: 0,
  message: ''
});

// WebSocket connection for real-time updates
useEffect(() => {
  const ws = new WebSocket(`ws://localhost:8000/ws/resources/${resourceId}/installation`);
  
  ws.onmessage = (event) => {
    const progressData = JSON.parse(event.data);
    setInstallationProgress(progressData);
    
    // Auto-advance to Step 3 when installation completes
    if (progressData.status === 'completed' && progressData.step === 'agent_start') {
      setCurrentStep(3);
    }
  };
  
  return () => ws.close();
}, [resourceId]);
```

#### **Installation Command Generation:**

```typescript
// Generate installation command with resource context
const generateInstallCommand = (resourceId: string) => {
  const token = generateSecureToken(); // Backend-generated token
  return `curl -fsSL "https://kumulus.cloud/install.sh?resource_id=${resourceId}&token=${token}" | sh`;
};
```

### **4. Backend API Enhancements Needed**

#### **Installation Script Endpoint:**
```typescript
// GET /install.sh?resource_id=xxx&token=yyy
app.get('/install.sh', async (req, res) => {
  const { resource_id, token } = req.query;
  
  // Validate token and resource_id
  const resource = await validateResourceToken(resource_id, token);
  if (!resource) {
    return res.status(401).send('Invalid token');
  }
  
  // Generate customized installation script
  const script = generateInstallScript({
    PROVIDER_ID: resource.provider_id,
    RESOURCE_ID: resource_id,
    PROVIDER_TOKEN: token,
    KUMULUS_API_URL: process.env.API_URL
  });
  
  res.setHeader('Content-Type', 'application/x-sh');
  res.send(script);
});
```

#### **Installation Progress Endpoint:**
```typescript
// POST /api/providers/{id}/installation
app.post('/api/providers/:id/installation', async (req, res) => {
  const { step, status, progress, message } = req.body;
  
  // Update installation progress in database
  await updateInstallationProgress(req.params.id, {
    step, status, progress, message, timestamp: new Date()
  });
  
  // Broadcast to WebSocket clients
  broadcastToResource(req.params.id, {
    type: 'installation_progress',
    data: { step, status, progress, message }
  });
  
  res.json({ success: true });
});
```

#### **WebSocket Support:**
```typescript
// WebSocket endpoint for real-time updates
app.ws('/ws/resources/:id/installation', (ws, req) => {
  const resourceId = req.params.id;
  
  // Add client to resource-specific room
  addWebSocketClient(resourceId, ws);
  
  ws.on('close', () => {
    removeWebSocketClient(resourceId, ws);
  });
});
```

### **5. Security Enhancements**

#### **Token-Based Authentication:**
- ✅ **Secure tokens** for installation script access
- ✅ **Time-limited tokens** (expire after 1 hour)
- ✅ **Resource-specific tokens** (can't be reused)

#### **Agent Authentication:**
- ✅ **Cryptographic signing** with auto-generated keys
- ✅ **Secure key storage** with proper permissions
- ✅ **Tamper-proof metrics** with signature verification

### **6. Deployment Recommendations**

#### **Production Setup:**
```bash
# The enhanced script now provides:
1. System requirements validation
2. Secure user and directory setup
3. Systemd service for automatic startup
4. Proper logging and monitoring
5. Integration with provider console
6. Real-time progress reporting
```

#### **Development vs Production:**
- **Development**: Direct Deno execution for quick testing
- **Production**: Systemd service with proper security and monitoring

### **7. Monitoring & Observability**

#### **Provider Console Integration:**
- ✅ **Real-time installation progress**
- ✅ **Agent health status monitoring**
- ✅ **System metrics dashboard**
- ✅ **Error reporting and troubleshooting**

#### **Agent Monitoring:**
- ✅ **Health check endpoints** (`/health`, `/status`)
- ✅ **Systemd service monitoring**
- ✅ **Structured logging** with log rotation
- ✅ **Performance metrics** collection

### **8. Next Steps**

#### **Immediate Actions:**
1. ✅ **Enhanced installation script** (completed)
2. 🔄 **Update provider console** Step 2 component
3. 🔄 **Add WebSocket support** to backend
4. 🔄 **Create installation progress** API endpoints
5. 🔄 **Add token generation** for secure script access

#### **Future Enhancements:**
- **Docker deployment option** for containerized environments
- **Multi-platform support** (ARM64, different Linux distros)
- **Automated updates** and version management
- **Advanced monitoring** with metrics collection

## 🎯 **Summary**

The integration now provides:
- **Seamless workflow** from console to agent installation
- **Real-time progress tracking** during installation
- **Production-ready deployment** with proper security
- **Comprehensive monitoring** and error handling
- **Secure communication** between all components

This eliminates the redundancy between the two scripts and provides a unified, production-ready solution that integrates perfectly with the provider console workflow.
