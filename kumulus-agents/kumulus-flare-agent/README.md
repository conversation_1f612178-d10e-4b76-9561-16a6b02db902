# Kumulus Flare Agent

A secure, high-performance agent for collecting system metrics and managing compute resources in the Kumulus network.

## 🚀 Features

### **Enhanced Security**
- **Secure Key Management**: Automatic key generation and rotation
- **Cryptographic Signing**: All data signed with Polkadot sr25519 keys
- **Tamper-Proof Metrics**: Prevents data manipulation by providers
- **Secure Storage**: Keys stored with restrictive permissions

### **Comprehensive System Monitoring**
- **Hardware Detection**: CPU, RAM, Storage, GPU information
- **Performance Metrics**: Real-time CPU usage, memory pressure, load averages
- **Container Monitoring**: Docker status, running/unhealthy containers
- **Network Information**: Public/private IP, bandwidth detection

### **Reliable Operation**
- **Health Checks**: Regular system health monitoring
- **Automatic Recovery**: Retry mechanisms and error handling
- **Graceful Shutdown**: Clean resource cleanup
- **Configuration Management**: Hot-reloadable configuration

## 📋 System Requirements

- **OS**: Linux (Ubuntu 20.04+ recommended)
- **Memory**: Minimum 1GB RAM
- **Storage**: Minimum 10GB free space
- **Network**: Internet connectivity required
- **Docker**: Required for containerized deployment

## 🔧 Installation

### **Quick Install (Recommended)**

```bash
curl -fsSL https://kumulus.cloud/install.sh | sh
```

### **Manual Installation**

```bash
# Install Deno (if not using Docker)
curl -fsSL https://deno.land/install.sh | sh

# Install Docker
curl -fsSL https://get.docker.com | sh

# Clone and build
git clone https://github.com/kollectvye/kumulus.git
cd kumulus/kumulus-agents/kumulus-flare-agent

# Docker deployment (recommended)
docker-compose up -d

# Or run directly with Deno
deno run --allow-all main.ts
```

## 🐳 Docker Deployment

```yaml
version: '3.8'
services:
  kumulus-agent:
    image: kumulus/flare-agent:latest
    restart: unless-stopped
    environment:
      - KUMULUS_API_URL=https://api.kumulus.cloud/v1
      - LOG_LEVEL=info
      - HEALTH_CHECK_INTERVAL=5
    volumes:
      - /etc/kumulus/keys:/etc/kumulus/keys:rw
      - /var/log/kumulus:/var/log/kumulus:rw
      - /var/run/docker.sock:/var/run/docker.sock:ro
    network_mode: host
```

## ⚙️ Configuration

### **Environment Variables**

| Variable | Default | Description |
|----------|---------|-------------|
| `KUMULUS_API_URL` | `https://api.kumulus.cloud/v1` | API endpoint |
| `LOG_LEVEL` | `info` | Logging level |
| `HEALTH_CHECK_INTERVAL` | `5` | Health check interval (minutes) |

## 🔐 Security Architecture

### **Key Management**
- Keys stored in `/etc/kumulus/keys/` with 600 permissions
- Automatic key generation on first run
- Support for key rotation (30-day default)

### **Data Integrity**
- All metrics signed with provider's private key
- Nonce and timestamp included in signatures
- Server-side signature verification

## 📊 Monitoring

### **Health Checks**
- HTTP endpoint: `http://localhost:8800/health`
- Status endpoint: `http://localhost:8800/status`

### **Logging**
```bash
# View logs
sudo journalctl -u kumulus-agent -f

# Docker logs
docker logs -f kumulus-flare-agent
```

## 🛠️ Development

```bash
# Run in development mode
deno run --allow-all --watch main.ts

# Run tests
deno test --allow-all

# Format code
deno fmt
```

## 📄 License

MIT License
