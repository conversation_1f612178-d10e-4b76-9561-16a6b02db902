#!/bin/bash

# Kumulus Flare Agent Installation Script
# This script installs and configures the Kumulus provider agent

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
KUMULUS_USER="kumulus"
KUMULUS_HOME="/opt/kumulus"
KUMULUS_CONFIG="/etc/kumulus"
KUMULUS_LOGS="/var/log/kumulus"
SERVICE_NAME="kumulus-agent"
GITHUB_REPO="kollectvye/kumulus"
INSTALL_METHOD="docker" # docker or binary

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons."
        error "Please run as a regular user with sudo privileges."
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check OS
    if [[ ! -f /etc/os-release ]]; then
        error "Unsupported operating system"
        exit 1
    fi
    
    source /etc/os-release
    info "Detected OS: $PRETTY_NAME"
    
    # Check architecture
    ARCH=$(uname -m)
    if [[ "$ARCH" != "x86_64" && "$ARCH" != "aarch64" ]]; then
        error "Unsupported architecture: $ARCH"
        exit 1
    fi
    
    # Check available memory (minimum 1GB)
    MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    MEMORY_GB=$((MEMORY_KB / 1024 / 1024))
    if [[ $MEMORY_GB -lt 1 ]]; then
        error "Insufficient memory. Minimum 1GB required, found ${MEMORY_GB}GB"
        exit 1
    fi
    
    # Check available disk space (minimum 10GB)
    DISK_SPACE=$(df / | awk 'NR==2 {print $4}')
    DISK_SPACE_GB=$((DISK_SPACE / 1024 / 1024))
    if [[ $DISK_SPACE_GB -lt 10 ]]; then
        error "Insufficient disk space. Minimum 10GB required, found ${DISK_SPACE_GB}GB"
        exit 1
    fi
    
    log "System requirements check passed"
}

# Install Docker if not present
install_docker() {
    if command -v docker &> /dev/null; then
        log "Docker is already installed"
        return
    fi
    
    log "Installing Docker..."
    
    # Update package index
    sudo apt-get update
    
    # Install prerequisites
    sudo apt-get install -y \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # Add Docker's official GPG key
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    
    # Set up the repository
    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
        $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker Engine
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log "Docker installed successfully"
}

# Create system user and directories
setup_system() {
    log "Setting up system user and directories..."
    
    # Create kumulus user if it doesn't exist
    if ! id "$KUMULUS_USER" &>/dev/null; then
        sudo useradd -r -s /bin/false -d "$KUMULUS_HOME" "$KUMULUS_USER"
        log "Created user: $KUMULUS_USER"
    fi
    
    # Create directories
    sudo mkdir -p "$KUMULUS_HOME" "$KUMULUS_CONFIG/keys" "$KUMULUS_LOGS"
    
    # Set permissions
    sudo chown -R "$KUMULUS_USER:$KUMULUS_USER" "$KUMULUS_HOME" "$KUMULUS_CONFIG" "$KUMULUS_LOGS"
    sudo chmod 700 "$KUMULUS_CONFIG/keys"
    sudo chmod 755 "$KUMULUS_LOGS"
    
    log "System setup completed"
}

# Download and install agent
install_agent() {
    log "Installing Kumulus agent..."
    
    if [[ "$INSTALL_METHOD" == "docker" ]]; then
        install_docker_agent
    else
        install_binary_agent
    fi
}

# Install Docker version
install_docker_agent() {
    log "Installing Docker-based agent..."
    
    # Create docker-compose.yml
    sudo tee "$KUMULUS_HOME/docker-compose.yml" > /dev/null <<EOF
version: '3.8'

services:
  kumulus-agent:
    image: kumulus/flare-agent:latest
    container_name: kumulus-flare-agent
    restart: unless-stopped
    
    environment:
      - KUMULUS_API_URL=\${KUMULUS_API_URL:-https://api.kumulus.cloud/v1}
      - LOG_LEVEL=info
      - HEALTH_CHECK_INTERVAL=5
    
    volumes:
      - $KUMULUS_CONFIG/keys:/etc/kumulus/keys:rw
      - $KUMULUS_LOGS:/var/log/kumulus:rw
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/host/root:ro
    
    network_mode: host
    
    security_opt:
      - no-new-privileges:true
    
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8800/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
EOF
    
    # Set ownership
    sudo chown "$KUMULUS_USER:$KUMULUS_USER" "$KUMULUS_HOME/docker-compose.yml"
    
    log "Docker agent configuration created"
}

# Create systemd service
create_service() {
    log "Creating systemd service..."
    
    sudo tee "/etc/systemd/system/$SERVICE_NAME.service" > /dev/null <<EOF
[Unit]
Description=Kumulus Flare Agent
Documentation=https://docs.kumulus.cloud
After=network.target docker.service
Requires=docker.service

[Service]
Type=forking
User=$KUMULUS_USER
Group=$KUMULUS_USER
WorkingDirectory=$KUMULUS_HOME
ExecStart=/usr/bin/docker compose up -d
ExecStop=/usr/bin/docker compose down
ExecReload=/usr/bin/docker compose restart
Restart=always
RestartSec=10
TimeoutStartSec=300
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$KUMULUS_CONFIG $KUMULUS_LOGS

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable service
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    
    log "Systemd service created and enabled"
}

# Start the agent
start_agent() {
    log "Starting Kumulus agent..."
    
    sudo systemctl start "$SERVICE_NAME"
    
    # Wait a moment and check status
    sleep 5
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log "Kumulus agent started successfully"
    else
        error "Failed to start Kumulus agent"
        sudo systemctl status "$SERVICE_NAME"
        exit 1
    fi
}

# Main installation function
main() {
    log "Starting Kumulus Flare Agent installation..."
    
    check_root
    check_requirements
    setup_system
    install_agent
    create_service
    start_agent
    
    log "Installation completed successfully!"
    info "Agent status: $(sudo systemctl is-active $SERVICE_NAME)"
    info "View logs: sudo journalctl -u $SERVICE_NAME -f"
    info "Agent configuration: $KUMULUS_CONFIG"
    
    warning "Please log out and back in for Docker group membership to take effect"
}

# Run main function
main "$@"
