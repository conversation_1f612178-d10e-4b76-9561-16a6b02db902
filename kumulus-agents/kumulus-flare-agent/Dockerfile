# Multi-stage build for Kumulus Flare Agent
FROM denoland/deno:2.1.4 AS builder

# Set working directory
WORKDIR /app

# Copy dependency files
COPY deno.json deno.lock ./

# Cache dependencies
RUN deno cache --lock=deno.lock deno.json

# Copy source code
COPY . .

# Compile the application
RUN deno compile --allow-all --output kumulus-agent main.ts

# Production stage
FROM debian:bookworm-slim

# Install required system packages
RUN apt-get update && apt-get install -y \
    curl \
    docker.io \
    procps \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create kumulus user and directories
RUN useradd -r -s /bin/false kumulus && \
    mkdir -p /etc/kumulus/keys /var/log/kumulus && \
    chown -R kumulus:kumulus /etc/kumulus /var/log/kumulus && \
    chmod 700 /etc/kumulus/keys

# Copy compiled binary
COPY --from=builder /app/kumulus-agent /usr/local/bin/

# Set permissions
RUN chmod +x /usr/local/bin/kumulus-agent

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Ensure directories exist with correct permissions\n\
mkdir -p /etc/kumulus/keys /var/log/kumulus\n\
chown -R kumulus:kumulus /etc/kumulus /var/log/kumulus\n\
chmod 700 /etc/kumulus/keys\n\
\n\
# Start the agent\n\
exec su kumulus -s /bin/bash -c "/usr/local/bin/kumulus-agent"\n\
' > /usr/local/bin/start-agent.sh && chmod +x /usr/local/bin/start-agent.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8800/health || exit 1

# Expose port
EXPOSE 8800

# Set user
USER kumulus

# Start the agent
CMD ["/usr/local/bin/start-agent.sh"]
