export interface SystemInfo {
  // Hardware Information
  cpu: {
    model: string;
    cores: number;
    threads: number;
    architecture: string;
    frequency: string;
  };
  memory: {
    total: number; // in MB
    available: number;
    used: number;
    usage_percent: number;
  };
  storage: {
    total: number; // in GB
    available: number;
    used: number;
    usage_percent: number;
    filesystem: string;
  };
  gpu?: {
    present: boolean;
    model?: string;
    memory?: number; // in MB
    driver_version?: string;
  };
  
  // Network Information
  network: {
    public_ip: string;
    local_ip: string;
    bandwidth_up?: number; // Mbps
    bandwidth_down?: number; // Mbps
  };
  
  // Operating System
  os: {
    name: string;
    version: string;
    kernel: string;
    uptime: number; // seconds
  };
  
  // Container Runtime
  containers: {
    runtime: string; // docker, podman, etc.
    version: string;
    running_count: number;
    unhealthy_count: number;
    total_count: number;
  };
  
  // Performance Metrics
  performance: {
    cpu_usage_percent: number;
    load_average: number[];
    memory_pressure: boolean;
    disk_io_wait: number;
    network_connections: number;
  };
  
  // Timestamps
  timestamp: {
    unix: number;
    iso: string;
    uptime: number;
  };
}

export class SystemInfoCollector {
  async collectFullSystemInfo(): Promise<SystemInfo> {
    const [
      cpuInfo,
      memoryInfo,
      storageInfo,
      gpuInfo,
      networkInfo,
      osInfo,
      containerInfo,
      performanceInfo
    ] = await Promise.all([
      this.getCPUInfo(),
      this.getMemoryInfo(),
      this.getStorageInfo(),
      this.getGPUInfo(),
      this.getNetworkInfo(),
      this.getOSInfo(),
      this.getContainerInfo(),
      this.getPerformanceInfo()
    ]);

    const now = new Date();
    return {
      cpu: cpuInfo,
      memory: memoryInfo,
      storage: storageInfo,
      gpu: gpuInfo,
      network: networkInfo,
      os: osInfo,
      containers: containerInfo,
      performance: performanceInfo,
      timestamp: {
        unix: Math.floor(now.getTime() / 1000),
        iso: now.toISOString(),
        uptime: await this.getSystemUptime()
      }
    };
  }

  private async getCPUInfo() {
    try {
      const cpuinfoCmd = new Deno.Command("cat", {
        args: ["/proc/cpuinfo"],
        stdout: "piped"
      });
      const result = await cpuinfoCmd.output();
      const output = new TextDecoder().decode(result.stdout);
      
      const modelMatch = output.match(/model name\s*:\s*(.+)/);
      const coreCount = (output.match(/processor\s*:/g) || []).length;
      const archCmd = new Deno.Command("uname", { args: ["-m"], stdout: "piped" });
      const archResult = await archCmd.output();
      const architecture = new TextDecoder().decode(archResult.stdout).trim();

      return {
        model: modelMatch ? modelMatch[1].trim() : "Unknown",
        cores: coreCount,
        threads: coreCount, // Simplified - could detect hyperthreading
        architecture,
        frequency: "Unknown" // Could be extracted from /proc/cpuinfo
      };
    } catch (error) {
      console.error("Failed to get CPU info:", error);
      return {
        model: "Unknown",
        cores: 0,
        threads: 0,
        architecture: "Unknown",
        frequency: "Unknown"
      };
    }
  }

  private async getMemoryInfo() {
    try {
      const memCmd = new Deno.Command("free", { args: ["-m"], stdout: "piped" });
      const result = await memCmd.output();
      const output = new TextDecoder().decode(result.stdout);
      
      const lines = output.split('\n');
      const memLine = lines[1].split(/\s+/);
      
      const total = parseInt(memLine[1]);
      const available = parseInt(memLine[6] || memLine[3]); // available or free
      const used = total - available;
      
      return {
        total,
        available,
        used,
        usage_percent: Math.round((used / total) * 100)
      };
    } catch (error) {
      console.error("Failed to get memory info:", error);
      return { total: 0, available: 0, used: 0, usage_percent: 0 };
    }
  }

  private async getStorageInfo() {
    try {
      const dfCmd = new Deno.Command("df", { args: ["-BG", "/"], stdout: "piped" });
      const result = await dfCmd.output();
      const output = new TextDecoder().decode(result.stdout);
      
      const lines = output.split('\n');
      const diskLine = lines[1].split(/\s+/);
      
      const total = parseInt(diskLine[1].replace('G', ''));
      const used = parseInt(diskLine[2].replace('G', ''));
      const available = parseInt(diskLine[3].replace('G', ''));
      
      return {
        total,
        available,
        used,
        usage_percent: Math.round((used / total) * 100),
        filesystem: diskLine[0]
      };
    } catch (error) {
      console.error("Failed to get storage info:", error);
      return { total: 0, available: 0, used: 0, usage_percent: 0, filesystem: "Unknown" };
    }
  }

  private async getGPUInfo() {
    try {
      const nvidiaCmd = new Deno.Command("nvidia-smi", {
        args: ["--query-gpu=name,memory.total,driver_version", "--format=csv,noheader,nounits"],
        stdout: "piped",
        stderr: "piped"
      });
      const result = await nvidiaCmd.output();
      
      if (result.success) {
        const output = new TextDecoder().decode(result.stdout).trim();
        const [model, memory, driver] = output.split(', ');
        
        return {
          present: true,
          model: model.trim(),
          memory: parseInt(memory),
          driver_version: driver.trim()
        };
      }
      
      return { present: false };
    } catch (error) {
      return { present: false };
    }
  }

  private async getNetworkInfo() {
    try {
      // Get public IP
      const publicIpResponse = await fetch("https://api.ipify.org?format=text", {
        signal: AbortSignal.timeout(5000)
      });
      const publicIp = await publicIpResponse.text();
      
      // Get local IP
      const hostnameCmd = new Deno.Command("hostname", { args: ["-I"], stdout: "piped" });
      const hostnameResult = await hostnameCmd.output();
      const localIp = new TextDecoder().decode(hostnameResult.stdout).trim().split(' ')[0];
      
      return {
        public_ip: publicIp.trim(),
        local_ip: localIp
      };
    } catch (error) {
      console.error("Failed to get network info:", error);
      return {
        public_ip: "Unknown",
        local_ip: "Unknown"
      };
    }
  }

  private async getOSInfo() {
    try {
      const [osRelease, kernel, uptime] = await Promise.all([
        this.runCommand("cat", ["/etc/os-release"]),
        this.runCommand("uname", ["-r"]),
        this.getSystemUptime()
      ]);
      
      const osName = osRelease.match(/PRETTY_NAME="([^"]+)"/)?.[1] || "Unknown";
      const osVersion = osRelease.match(/VERSION="([^"]+)"/)?.[1] || "Unknown";
      
      return {
        name: osName,
        version: osVersion,
        kernel: kernel.trim(),
        uptime
      };
    } catch (error) {
      console.error("Failed to get OS info:", error);
      return {
        name: "Unknown",
        version: "Unknown",
        kernel: "Unknown",
        uptime: 0
      };
    }
  }

  private async getContainerInfo() {
    try {
      const [version, running, total] = await Promise.all([
        this.runCommand("docker", ["--version"]),
        this.runCommand("docker", ["ps", "-q"]),
        this.runCommand("docker", ["ps", "-aq"])
      ]);
      
      const runningCount = running.split('\n').filter(line => line.trim()).length;
      const totalCount = total.split('\n').filter(line => line.trim()).length;
      
      // Check for unhealthy containers
      const unhealthy = await this.runCommand("docker", ["ps", "--filter", "health=unhealthy", "-q"]);
      const unhealthyCount = unhealthy.split('\n').filter(line => line.trim()).length;
      
      return {
        runtime: "docker",
        version: version.replace("Docker version ", "").trim(),
        running_count: runningCount,
        unhealthy_count: unhealthyCount,
        total_count: totalCount
      };
    } catch (error) {
      return {
        runtime: "none",
        version: "Unknown",
        running_count: 0,
        unhealthy_count: 0,
        total_count: 0
      };
    }
  }

  private async getPerformanceInfo() {
    try {
      const [cpuUsage, loadAvg, memPressure] = await Promise.all([
        this.getCPUUsage(),
        this.getLoadAverage(),
        this.checkMemoryPressure()
      ]);
      
      return {
        cpu_usage_percent: cpuUsage,
        load_average: loadAvg,
        memory_pressure: memPressure,
        disk_io_wait: 0, // Could be implemented
        network_connections: 0 // Could be implemented
      };
    } catch (error) {
      return {
        cpu_usage_percent: 0,
        load_average: [0, 0, 0],
        memory_pressure: false,
        disk_io_wait: 0,
        network_connections: 0
      };
    }
  }

  private async getCPUUsage(): Promise<number> {
    try {
      const topCmd = new Deno.Command("top", { args: ["-bn1"], stdout: "piped" });
      const result = await topCmd.output();
      const output = new TextDecoder().decode(result.stdout);
      
      const cpuLine = output.split('\n').find(line => line.includes('Cpu(s)'));
      if (cpuLine) {
        const match = cpuLine.match(/(\d+\.\d+)%?\s*us/);
        return match ? parseFloat(match[1]) : 0;
      }
      return 0;
    } catch {
      return 0;
    }
  }

  private async getLoadAverage(): Promise<number[]> {
    try {
      const loadavg = await Deno.readTextFile("/proc/loadavg");
      return loadavg.split(' ').slice(0, 3).map(parseFloat);
    } catch {
      return [0, 0, 0];
    }
  }

  private async checkMemoryPressure(): Promise<boolean> {
    try {
      const meminfo = await Deno.readTextFile("/proc/meminfo");
      const available = meminfo.match(/MemAvailable:\s*(\d+)/)?.[1];
      const total = meminfo.match(/MemTotal:\s*(\d+)/)?.[1];
      
      if (available && total) {
        const availablePercent = (parseInt(available) / parseInt(total)) * 100;
        return availablePercent < 10; // Less than 10% available = pressure
      }
      return false;
    } catch {
      return false;
    }
  }

  private async getSystemUptime(): Promise<number> {
    try {
      const uptime = await Deno.readTextFile("/proc/uptime");
      return parseFloat(uptime.split(' ')[0]);
    } catch {
      return 0;
    }
  }

  private async runCommand(cmd: string, args: string[]): Promise<string> {
    try {
      const command = new Deno.Command(cmd, { args, stdout: "piped" });
      const result = await command.output();
      return new TextDecoder().decode(result.stdout);
    } catch {
      return "";
    }
  }
}
