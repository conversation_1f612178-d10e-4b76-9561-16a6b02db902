import { cryptoWaitReady } from "https://deno.land/x/polkadot@0.2.45/util-crypto/mod.ts";
import { Keyring } from "https://deno.land/x/polkadot@0.2.45/keyring/mod.ts";
import { KeyringPair } from "https://deno.land/x/polkadot@0.2.45/keyring/types.ts";

export class SecureKeyManager {
  private keyring: Keyring | null = null;
  private keyPair: KeyringPair | null = null;
  private readonly keyStorePath = "/etc/kumulus/keys";
  private readonly keyFileName = "provider.key";

  constructor() {
    this.ensureKeyDirectory();
  }

  private ensureKeyDirectory() {
    try {
      Deno.mkdirSync(this.keyStorePath, { recursive: true });
      // Set restrictive permissions (700 - owner only)
      Deno.chmodSync(this.keyStorePath, 0o700);
    } catch (error) {
      console.error("Failed to create key directory:", error);
    }
  }

  async initialize(): Promise<boolean> {
    try {
      await cryptoWaitReady();
      this.keyring = new Keyring({ type: 'sr25519' });

      // Try to load existing key first
      if (await this.loadExistingKey()) {
        console.log("✅ Loaded existing provider key");
        return true;
      }

      // Generate new key if none exists
      if (await this.generateNewKey()) {
        console.log("✅ Generated new provider key");
        return true;
      }

      return false;
    } catch (error) {
      console.error("❌ Key manager initialization failed:", error);
      return false;
    }
  }

  private async loadExistingKey(): Promise<boolean> {
    try {
      const keyPath = `${this.keyStorePath}/${this.keyFileName}`;
      const keyData = await Deno.readTextFile(keyPath);
      const { mnemonic, metadata } = JSON.parse(keyData);
      
      this.keyPair = this.keyring!.createFromUri(mnemonic);
      
      console.log(`Key loaded - Address: ${this.keyPair.address}`);
      console.log(`Key metadata:`, metadata);
      
      return true;
    } catch (error) {
      // File doesn't exist or is corrupted
      return false;
    }
  }

  private async generateNewKey(): Promise<boolean> {
    try {
      // Generate a new mnemonic
      const mnemonic = this.keyring!.generateMnemonic();
      this.keyPair = this.keyring!.createFromUri(mnemonic);

      const keyData = {
        mnemonic,
        address: this.keyPair.address,
        metadata: {
          created: new Date().toISOString(),
          version: "1.0",
          type: "provider-key"
        }
      };

      const keyPath = `${this.keyStorePath}/${this.keyFileName}`;
      await Deno.writeTextFile(keyPath, JSON.stringify(keyData, null, 2));
      
      // Set restrictive permissions (600 - owner read/write only)
      Deno.chmodSync(keyPath, 0o600);

      return true;
    } catch (error) {
      console.error("Failed to generate new key:", error);
      return false;
    }
  }

  getKeyPair(): KeyringPair | null {
    return this.keyPair;
  }

  getAddress(): string {
    return this.keyPair?.address || "";
  }

  async rotateKey(): Promise<boolean> {
    try {
      // Backup old key
      const backupPath = `${this.keyStorePath}/${this.keyFileName}.backup.${Date.now()}`;
      const currentKeyPath = `${this.keyStorePath}/${this.keyFileName}`;
      
      try {
        await Deno.copyFile(currentKeyPath, backupPath);
      } catch {
        // No existing key to backup
      }

      // Generate new key
      if (await this.generateNewKey()) {
        console.log("✅ Key rotation completed");
        return true;
      }

      return false;
    } catch (error) {
      console.error("❌ Key rotation failed:", error);
      return false;
    }
  }

  // Secure key derivation for different purposes
  deriveKey(purpose: string): KeyringPair | null {
    if (!this.keyPair) return null;
    
    const derivationPath = `//${purpose}`;
    return this.keyPair.derive(derivationPath);
  }
}
