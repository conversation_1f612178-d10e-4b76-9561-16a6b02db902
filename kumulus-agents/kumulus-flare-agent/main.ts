import { StatsCollector } from "./stats-collector.ts";
import { app } from "./provisioner.ts";
import { ConfigManager } from "./config-manager.ts";

class KumulusFlareAgent {
  private statsCollector: StatsCollector;
  private configManager: ConfigManager;
  private isRunning = false;

  constructor() {
    this.statsCollector = new StatsCollector();
    this.configManager = new ConfigManager();
  }

  async initialize(): Promise<boolean> {
    try {
      console.log("🚀 Starting Kumulus Flare Agent...");

      // Load configuration
      if (!await this.configManager.loadConfig()) {
        throw new Error("Failed to load configuration");
      }

      // Apply environment overrides
      this.configManager.applyEnvironmentOverrides();

      // Validate configuration
      const validation = this.configManager.validateConfig();
      if (!validation.valid) {
        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
      }

      console.log("✅ Configuration loaded and validated");

      // Initialize stats collector
      if (!await this.statsCollector.initialize()) {
        throw new Error("Failed to initialize stats collector");
      }
      console.log("✅ Stats collector initialized");

      return true;
    } catch (error) {
      console.error("❌ Failed to initialize agent:", error);
      return false;
    }
  }

  async start(): Promise<void> {
    if (!await this.initialize()) {
      Deno.exit(1);
    }

    this.isRunning = true;
    console.log("🔄 Agent started successfully");

    // Perform full provider setup
    const setupSuccess = await this.statsCollector.performFullSetup();
    if (!setupSuccess) {
      console.error("❌ Provider setup failed");
      Deno.exit(1);
    }

    // Start health monitoring
    this.startHealthMonitoring();

    // Setup graceful shutdown
    this.setupGracefulShutdown();

    // Keep the process running
    while (this.isRunning) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  private startHealthMonitoring(): void {
    // Log status every 5 minutes
    setInterval(() => {
      if (this.isRunning) {
        const state = this.statsCollector.getState();
        console.log(`💓 Agent Status - Registered: ${state.isRegistered}, Validated: ${state.isValidated}, Last Health Check: ${new Date(state.lastHealthCheck).toISOString()}`);
      }
    }, 5 * 60 * 1000);
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
      this.isRunning = false;

      // Stop health check interval
      this.statsCollector.stopHealthCheckInterval();

      // Save final state
      await this.configManager.saveConfig();

      console.log("✅ Shutdown complete");
      Deno.exit(0);
    };

    // Handle various shutdown signals
    Deno.addSignalListener("SIGINT", () => shutdown("SIGINT"));
    Deno.addSignalListener("SIGTERM", () => shutdown("SIGTERM"));
  }

  async getStatus(): Promise<object> {
    const state = this.statsCollector.getState();
    const config = this.configManager.getConfig();

    return {
      agent: {
        running: this.isRunning,
        version: "2.0.0",
        uptime: Date.now() - (state.registrationTime || Date.now())
      },
      provider: {
        address: this.statsCollector.getAddress(),
        registered: state.isRegistered,
        validated: state.isValidated,
        last_health_check: state.lastHealthCheck,
        public_ip: state.ipAddress
      },
      config: {
        api_url: config.api.base_url,
        health_check_interval: config.health_check.interval_minutes,
        log_level: config.logging.level
      }
    };
  }
}

// Enhanced HTTP server with health checks and provisioner
async function startHttpServer(agent: KumulusFlareAgent): Promise<void> {
  const server = Deno.serve({ port: 8800, hostname: "0.0.0.0" }, async (req) => {
    const url = new URL(req.url);

    // Health check endpoint
    if (url.pathname === "/health") {
      return new Response("OK", { status: 200 });
    }

    // Status endpoint
    if (url.pathname === "/status") {
      const status = await agent.getStatus();
      return new Response(JSON.stringify(status, null, 2), {
        headers: { "Content-Type": "application/json" }
      });
    }

    // Delegate to provisioner for other routes
    return app.fetch(req);
  });

  console.log("🌐 HTTP server started on port 8800");
  console.log("   Health check: http://localhost:8800/health");
  console.log("   Status: http://localhost:8800/status");
}

async function main() {
  const agent = new KumulusFlareAgent();

  // Start HTTP server
  startHttpServer(agent);

  // Start the main agent
  await agent.start();
}

if (import.meta.main) {
  main().catch((error) => {
    console.error("❌ Fatal error:", error);
    Deno.exit(1);
  });
}

// Run cleanup every 24 hours
// Deno.cron("Unused Resources Cleanup Cron","0 0 * * *", cleanupUnusedResources);
