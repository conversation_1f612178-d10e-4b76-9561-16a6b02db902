import {
  stringToU8a,
  u8aToHex,
} from "https://deno.land/x/polkadot@0.2.45/util/mod.ts";
import "jsr:@std/dotenv/load";
import { SecureKeyManager } from "./secure-key-manager.ts";
import { SystemInfoCollector, SystemInfo } from "./system-info-collector.ts";

interface ProviderState {
  isValidated: boolean;
  isRegistered: boolean;
  ipAddress: string;
  lastHealthCheck: number;
  registrationTime: number;
}

interface HealthCheckData {
  system_info: SystemInfo;
  provider_state: ProviderState;
  signature_info: {
    timestamp: number;
    nonce: string;
  };
}

export class StatsCollector {
  private state: ProviderState = {
    isValidated: false,
    isRegistered: false,
    ipAddress: "",
    lastHealthCheck: 0,
    registrationTime: 0,
  };

  private keyManager: SecureKeyManager;
  private systemCollector: SystemInfoCollector;
  private healthCheckInterval: number | null = null;

  base_url = Deno.env.get("KUMULUS_API_URL") || "http://localhost:8000/kumulus";
  providers_url: string;
  healthstats_url: string;

  constructor() {
    this.keyManager = new SecureKeyManager();
    this.systemCollector = new SystemInfoCollector();
    this.providers_url = `${this.base_url}/providers`;
    this.healthstats_url = `${this.base_url}/healthstats`;
  }

  async initialize(): Promise<boolean> {
    try {
      const keyInitialized = await this.keyManager.initialize();
      if (!keyInitialized) {
        console.error("❌ Failed to initialize key manager");
        return false;
      }

      console.log(`✅ Stats collector initialized with address: ${this.keyManager.getAddress()}`);
      return true;
    } catch (error) {
      console.error("❌ Stats collector initialization failed:", error);
      return false;
    }
  }

  private generateNonce(): string {
    return Math.random().toString(36).substring(2, 15) +
           Math.random().toString(36).substring(2, 15);
  }

  async signMessage(message: string): Promise<{ signature: string; address: string; nonce: string; timestamp: number }> {
    const keyPair = this.keyManager.getKeyPair();
    if (!keyPair) {
      throw new Error("Key pair not initialized. Call initialize() first.");
    }

    const nonce = this.generateNonce();
    const timestamp = Date.now();
    const messageWithMeta = JSON.stringify({
      message,
      nonce,
      timestamp
    });

    return {
      signature: u8aToHex(keyPair.sign(stringToU8a(messageWithMeta))),
      address: keyPair.address,
      nonce,
      timestamp
    };
  }

  async collectHealthCheckData(): Promise<HealthCheckData> {
    const systemInfo = await this.systemCollector.collectFullSystemInfo();

    return {
      system_info: systemInfo,
      provider_state: {
        ...this.state,
        lastHealthCheck: Date.now()
      },
      signature_info: {
        timestamp: Date.now(),
        nonce: this.generateNonce()
      }
    };
  }

  async sendSystemRegistration(): Promise<boolean> {
    try {
      const systemInfo = await this.systemCollector.collectFullSystemInfo();
      const registrationData = {
        provider_address: this.keyManager.getAddress(),
        system_info: systemInfo,
        registration_time: Date.now(),
        version: "2.0"
      };

      const message = JSON.stringify(registrationData);
      const signedMessage = await this.signMessage(message);

      const response = await fetch(`${this.providers_url}/register`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          data: registrationData,
          signature: signedMessage.signature,
          address: signedMessage.address,
          nonce: signedMessage.nonce,
          timestamp: signedMessage.timestamp
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Registration failed: ${response.status} - ${errorData}`);
      }

      this.state.isRegistered = true;
      this.state.registrationTime = Date.now();
      console.log("✅ Provider registered successfully");
      return true;
    } catch (error) {
      console.error("❌ Error during registration:", error);
      return false;
    }
  }

  async sendHealthCheck(): Promise<boolean> {
    try {
      if (!this.healthstats_url) {
        throw new Error("Health stats URL is not set.");
      }

      const healthData = await this.collectHealthCheckData();
      const message = JSON.stringify(healthData);
      const signedMessage = await this.signMessage(message);

      const response = await fetch(this.healthstats_url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          data: healthData,
          signature: signedMessage.signature,
          address: signedMessage.address,
          nonce: signedMessage.nonce,
          timestamp: signedMessage.timestamp
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(
          `HTTP error! status: ${response.status}, body: ${errorData}`,
        );
      }

      this.state.lastHealthCheck = Date.now();
      console.log("✅ Health check sent successfully");
      return true;
    } catch (error) {
      console.error("❌ Error sending health check:", error);
      return false;
    }
  }

  async checkProviderRegistration(): Promise<boolean> {
    try {
      const address = this.keyManager.getAddress();
      if (!address) {
        throw new Error("Provider address not available");
      }

      const response = await fetch(`${this.providers_url}/${address}`);
      if (!response.ok) {
        console.error("❌ Provider Registration check failed:", response.status);
        return false;
      }

      const data = await response.json();
      this.state.isRegistered = !!data.address;
      console.log(
        `🔄 Provider registration status: ${
          this.state.isRegistered ? "registered" : "not registered"
        }`,
      );
      return this.state.isRegistered;
    } catch (error) {
      console.error("❌ Error checking provider registration:", error);
      return false;
    }
  }

  async checkProviderValidation(): Promise<boolean> {
    try {
      const address = this.keyManager.getAddress();
      if (!address) {
        throw new Error("Provider address not available");
      }

      const response = await fetch(`${this.providers_url}/${address}/validation`);
      if (!response.ok) {
        console.error("❌ Provider Validation check failed:", response.status);
        return false;
      }

      const data = await response.json();
      this.state.isValidated = !!data.validated;
      console.log(
        `🔄 Provider Validation status: ${
          this.state.isValidated ? "validated" : "not validated"
        }`,
      );
      return this.state.isValidated;
    } catch (error) {
      console.error("❌ Error checking provider validation:", error);
      return false;
    }
  }

  async updateNetworkInfo(): Promise<boolean> {
    try {
      // Get public IP address with timeout
      const ipResponse = await fetch("https://api.ipify.org?format=text", {
        signal: AbortSignal.timeout(5000)
      });
      const ipAddress = await ipResponse.text();
      this.state.ipAddress = ipAddress.trim();

      const networkData = {
        provider_address: this.keyManager.getAddress(),
        public_ip: this.state.ipAddress,
        timestamp: Date.now()
      };

      const signedMessage = await this.signMessage(JSON.stringify(networkData));

      const response = await fetch(`${this.healthstats_url}/network`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          data: networkData,
          signature: signedMessage.signature,
          address: signedMessage.address,
          nonce: signedMessage.nonce,
          timestamp: signedMessage.timestamp
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorData}`);
      }

      console.log("✅ Network info updated successfully");
      return true;
    } catch (error) {
      console.error("❌ Error updating network info:", error);
      return false;
    }
  }

  getState(): ProviderState {
    return { ...this.state };
  }

  getAddress(): string {
    return this.keyManager.getAddress();
  }

  async startHealthCheckInterval(intervalMinutes: number = 5): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    const intervalMs = intervalMinutes * 60 * 1000;
    this.healthCheckInterval = setInterval(async () => {
      if (this.state.isRegistered) {
        await this.sendHealthCheck();
      }
    }, intervalMs);

    console.log(`✅ Health check interval started (every ${intervalMinutes} minutes)`);
  }

  stopHealthCheckInterval(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log("✅ Health check interval stopped");
    }
  }

  async performFullSetup(): Promise<boolean> {
    try {
      console.log("🚀 Starting provider setup...");

      // Initialize key manager
      if (!await this.initialize()) {
        throw new Error("Failed to initialize key manager");
      }

      // Check if already registered
      const isRegistered = await this.checkProviderRegistration();
      if (!isRegistered) {
        // Register the provider
        if (!await this.sendSystemRegistration()) {
          throw new Error("Failed to register provider");
        }
      }

      // Update network information
      await this.updateNetworkInfo();

      // Send initial health check
      await this.sendHealthCheck();

      // Start regular health checks
      await this.startHealthCheckInterval(5); // Every 5 minutes

      console.log("✅ Provider setup completed successfully");
      return true;
    } catch (error) {
      console.error("❌ Provider setup failed:", error);
      return false;
    }
  }
}
