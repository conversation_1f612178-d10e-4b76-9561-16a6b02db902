export interface AgentConfig {
  // API Configuration
  api: {
    base_url: string;
    timeout_ms: number;
    retry_attempts: number;
    retry_delay_ms: number;
  };
  
  // Health Check Configuration
  health_check: {
    interval_minutes: number;
    startup_delay_seconds: number;
    failure_threshold: number;
  };
  
  // Security Configuration
  security: {
    key_rotation_days: number;
    signature_algorithm: string;
    enable_encryption: boolean;
  };
  
  // System Monitoring
  monitoring: {
    collect_gpu_info: boolean;
    collect_network_stats: boolean;
    collect_container_stats: boolean;
    performance_sampling_seconds: number;
  };
  
  // Logging Configuration
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    file_path: string;
    max_file_size_mb: number;
    max_files: number;
  };
  
  // Resource Limits
  resources: {
    max_memory_mb: number;
    max_cpu_percent: number;
    max_disk_usage_gb: number;
  };
}

export class ConfigManager {
  private config: AgentConfig;
  private configPath: string;

  constructor(configPath: string = "/etc/kumulus/agent.json") {
    this.configPath = configPath;
    this.config = this.getDefaultConfig();
  }

  private getDefaultConfig(): AgentConfig {
    return {
      api: {
        base_url: Deno.env.get("KUMULUS_API_URL") || "https://api.kumulus.cloud/v1",
        timeout_ms: 30000,
        retry_attempts: 3,
        retry_delay_ms: 1000
      },
      health_check: {
        interval_minutes: parseInt(Deno.env.get("HEALTH_CHECK_INTERVAL") || "5"),
        startup_delay_seconds: 30,
        failure_threshold: 3
      },
      security: {
        key_rotation_days: 30,
        signature_algorithm: "sr25519",
        enable_encryption: true
      },
      monitoring: {
        collect_gpu_info: true,
        collect_network_stats: true,
        collect_container_stats: true,
        performance_sampling_seconds: 10
      },
      logging: {
        level: (Deno.env.get("LOG_LEVEL") as any) || "info",
        file_path: "/var/log/kumulus/agent.log",
        max_file_size_mb: 10,
        max_files: 5
      },
      resources: {
        max_memory_mb: 256,
        max_cpu_percent: 50,
        max_disk_usage_gb: 1
      }
    };
  }

  async loadConfig(): Promise<boolean> {
    try {
      const configData = await Deno.readTextFile(this.configPath);
      const loadedConfig = JSON.parse(configData);
      
      // Merge with defaults to ensure all fields are present
      this.config = this.mergeConfigs(this.getDefaultConfig(), loadedConfig);
      
      console.log("✅ Configuration loaded successfully");
      return true;
    } catch (error) {
      if (error instanceof Deno.errors.NotFound) {
        console.log("📝 No config file found, creating default configuration");
        return await this.saveConfig();
      } else {
        console.error("❌ Failed to load configuration:", error);
        return false;
      }
    }
  }

  async saveConfig(): Promise<boolean> {
    try {
      // Ensure directory exists
      const configDir = this.configPath.substring(0, this.configPath.lastIndexOf('/'));
      await Deno.mkdir(configDir, { recursive: true });
      
      await Deno.writeTextFile(
        this.configPath, 
        JSON.stringify(this.config, null, 2)
      );
      
      // Set restrictive permissions
      await Deno.chmod(this.configPath, 0o600);
      
      console.log("✅ Configuration saved successfully");
      return true;
    } catch (error) {
      console.error("❌ Failed to save configuration:", error);
      return false;
    }
  }

  private mergeConfigs(defaultConfig: AgentConfig, userConfig: any): AgentConfig {
    const merged = { ...defaultConfig };
    
    for (const [key, value] of Object.entries(userConfig)) {
      if (key in merged && typeof value === 'object' && !Array.isArray(value)) {
        (merged as any)[key] = { ...(merged as any)[key], ...value };
      } else {
        (merged as any)[key] = value;
      }
    }
    
    return merged;
  }

  getConfig(): AgentConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<AgentConfig>): void {
    this.config = this.mergeConfigs(this.config, updates);
  }

  // Validation methods
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Validate API URL
    try {
      new URL(this.config.api.base_url);
    } catch {
      errors.push("Invalid API base URL");
    }
    
    // Validate intervals
    if (this.config.health_check.interval_minutes < 1) {
      errors.push("Health check interval must be at least 1 minute");
    }
    
    if (this.config.health_check.startup_delay_seconds < 0) {
      errors.push("Startup delay cannot be negative");
    }
    
    // Validate resource limits
    if (this.config.resources.max_memory_mb < 64) {
      errors.push("Maximum memory must be at least 64MB");
    }
    
    if (this.config.resources.max_cpu_percent < 1 || this.config.resources.max_cpu_percent > 100) {
      errors.push("CPU percentage must be between 1 and 100");
    }
    
    // Validate logging level
    const validLevels = ['debug', 'info', 'warn', 'error'];
    if (!validLevels.includes(this.config.logging.level)) {
      errors.push(`Invalid logging level. Must be one of: ${validLevels.join(', ')}`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Environment variable overrides
  applyEnvironmentOverrides(): void {
    const envMappings = {
      'KUMULUS_API_URL': 'api.base_url',
      'HEALTH_CHECK_INTERVAL': 'health_check.interval_minutes',
      'LOG_LEVEL': 'logging.level',
      'MAX_MEMORY_MB': 'resources.max_memory_mb',
      'MAX_CPU_PERCENT': 'resources.max_cpu_percent'
    };

    for (const [envVar, configPath] of Object.entries(envMappings)) {
      const envValue = Deno.env.get(envVar);
      if (envValue) {
        this.setNestedValue(this.config, configPath, this.parseEnvValue(envValue));
      }
    }
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  private parseEnvValue(value: string): any {
    // Try to parse as number
    if (!isNaN(Number(value))) {
      return Number(value);
    }
    
    // Try to parse as boolean
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;
    
    // Return as string
    return value;
  }

  // Configuration monitoring
  async watchConfigFile(callback: () => void): Promise<void> {
    try {
      const watcher = Deno.watchFs(this.configPath);
      
      for await (const event of watcher) {
        if (event.kind === "modify") {
          console.log("📝 Configuration file changed, reloading...");
          await this.loadConfig();
          callback();
        }
      }
    } catch (error) {
      console.error("❌ Failed to watch config file:", error);
    }
  }

  // Export configuration for debugging
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  // Import configuration from string
  importConfig(configJson: string): boolean {
    try {
      const importedConfig = JSON.parse(configJson);
      const validation = this.validateConfig();
      
      if (!validation.valid) {
        console.error("❌ Invalid configuration:", validation.errors);
        return false;
      }
      
      this.config = this.mergeConfigs(this.getDefaultConfig(), importedConfig);
      return true;
    } catch (error) {
      console.error("❌ Failed to import configuration:", error);
      return false;
    }
  }
}
