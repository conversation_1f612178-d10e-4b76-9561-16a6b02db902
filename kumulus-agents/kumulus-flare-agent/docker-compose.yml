version: '3.8'

services:
  kumulus-agent:
    build: .
    container_name: kumulus-flare-agent
    restart: unless-stopped
    
    # Environment variables
    environment:
      - KUMULUS_API_URL=${KUMULUS_API_URL:-http://localhost:8000/kumulus}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-5}
    
    # Volumes for persistent data
    volumes:
      - kumulus-keys:/etc/kumulus/keys:rw
      - kumulus-logs:/var/log/kumulus:rw
      - /var/run/docker.sock:/var/run/docker.sock:ro  # For Docker monitoring
      - /proc:/host/proc:ro                           # For system metrics
      - /sys:/host/sys:ro                             # For system metrics
      - /:/host/root:ro                               # For disk metrics
    
    # Network configuration
    network_mode: host
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8800/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  kumulus-keys:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /etc/kumulus/keys
  
  kumulus-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/kumulus
