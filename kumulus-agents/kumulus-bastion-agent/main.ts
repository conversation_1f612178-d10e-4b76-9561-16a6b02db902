import { Hono } from "@hono/hono";
import "jsr:@std/dotenv/load";

const app = new Hono();
let bastionId: string | null = null;
let bastionManagerUrl: string | null = null;

// Track running tunnels in memory
const runningTunnels: Record<
  string,
  { process: Deno.ChildProcess; sshCommand: string }
> = {};

const usedPorts = new Set<number>();

function findFreePort(start: number, end: number): number | null {
  for (let port = start; port <= end; port++) {
    if (!usedPorts.has(port)) return port;
  }
  return null;
}

app.get("/", (c) => {
  console.log("[BastionAgent] Health check");
  return c.text("Bastion Agent is running!");
});

// Register this bastion agent to the Bastion Manager
app.post("/register", async (c) => {
  const body = await c.req.json();
  const { name, ip_address } = body;

  if (!name || !ip_address) {
    return c.json({ ok: false, error: "Missing required fields" }, 400);
  }

  // Notify Bastion Manager
  try {
    const bastionManagerUrl =
      "https://test-kumulus-backend-zswhd8p5azvq.deno.dev"; // Deno.env.get("KOLLECTYVE_BACKEND_URL") ||  "http://localhost:8000/bastion";
    const res = await fetch(`${bastionManagerUrl}/bastions/register`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name, ip_address }),
    });
    if (!res.ok) {
      const errMsg = await res.text();
      console.error(
        `[BastionAgent] Registration failed with manager: ${errMsg}`,
      );
      return c.json({
        ok: false,
        error: "Failed to register with Bastion Manager",
      }, 500);
    }
    console.log(
      `[BastionAgent] Registered with manager: ${name} at ${ip_address}`,
    );
    return c.json({
      ok: true,
      message: "Bastion agent registered successfully",
    });
  } catch (err) {
    console.error(`[BastionAgent] Registration error:`, err);
    return c.json({ ok: false, error: "Registration error" }, 500);
  }
});

// Create/start a tunnel
app.post("/tunnel", async (c) => {
  const { tunnel_id, ssh_command, requested_port } = await c.req.json();
  if (runningTunnels[tunnel_id]) {
    return c.json(
      { ok: false, error: "Tunnel already running", tunnel_id },
      409,
    );
  }
  if (requested_port && usedPorts.has(requested_port)) {
    return c.json({
      ok: false,
      error: "Port already in use",
      tunnel_id,
      port: requested_port,
    }, 409);
  }

  let port = requested_port;
  if (!port) {
    const PORT_RANGE_START = Number(Deno.env.get("PORT_RANGE_START") || 30000);
    const PORT_RANGE_END = Number(Deno.env.get("PORT_RANGE_END") || 30100);
    port = findFreePort(PORT_RANGE_START, PORT_RANGE_END);
    if (!port) {
      return c.json({ ok: false, error: "No free port available" }, 503);
    }
  }
  try {
    const finalSshCommand = ssh_command.replace(/\{PORT\}/g, String(port));
    const [cmd, ...args] = ["bash", "-c", finalSshCommand];
    const process = new Deno.Command(cmd, {
      args,
      stdin: "null",
      stdout: "null",
      stderr: "null",
    }).spawn();
    runningTunnels[tunnel_id] = { process, sshCommand: finalSshCommand };
    usedPorts.add(port);
    return c.json({ ok: true, started: true, tunnel_id, port });
  } catch (e) {
    const errMsg = e instanceof Error ? e.message : String(e);
    return c.json({ ok: false, error: errMsg, tunnel_id }, 500);
  }
});

// Stop/delete a tunnel (release port)
app.delete("/tunnel/:tunnel_id", (c) => {
  const { tunnel_id } = c.req.param();
  const tunnel = runningTunnels[tunnel_id];
  if (!tunnel) {
    return c.json({ ok: false, error: "Tunnel not found", tunnel_id }, 404);
  }
  try {
    tunnel.process.kill("SIGTERM");
    // Extract port from sshCommand if possible
    const portMatch = tunnel.sshCommand.match(/:(\d+)/);
    if (portMatch) {
      usedPorts.delete(Number(portMatch[1]));
    }
    delete runningTunnels[tunnel_id];
    return c.json({ ok: true, stopped: true, tunnel_id });
  } catch (e) {
    const errMsg = e instanceof Error ? e.message : String(e);
    return c.json({ ok: false, error: errMsg, tunnel_id }, 500);
  }
});

// Push-based provider key sync
app.post("/sync-keys", async (c) => {
  try {
    const bastionManagerUrl = Deno.env.get("BASTION_MANAGER_URL") ||
      "http://localhost:8000/bastion";
    const res = await fetch(`${bastionManagerUrl}/provider-keys`);
    if (!res.ok) throw new Error("Failed to fetch provider keys");
    const keys = await res.text();
    await Deno.writeTextFile("/home/<USER>/.ssh/authorized_keys", keys);
    await Deno.chmod("/home/<USER>/.ssh/authorized_keys", 0o600);
    console.log("[LOG] Updated authorized_keys with provider keys (push)");
    return c.json({ ok: true, message: "authorized_keys updated" });
  } catch (err) {
    console.error("[ERROR] Failed to sync authorized_keys (push):", err);
    return c.json({
      ok: false,
      error: err instanceof Error ? err.message : String(err),
    }, 500);
  }
});

// Helper to detect public IP, with fallback
async function detectPublicIP(): Promise<string | null> {
  const services = ["https://ifconfig.me", "https://api.ipify.org"];
  for (const url of services) {
    try {
      const res = await fetch(url);
      if (res.ok) {
        const ip = (await res.text()).trim();
        if (ip && ip.match(/^\d+\.\d+\.\d+\.\d+$/)) return ip;
      }
    } catch (err) {
      console.warn(
        `[BastionAgent] Could not detect public IP from ${url}:`,
        err,
      );
    }
  }
  return null;
}

async function sendHeartbeat() {
  if (!bastionId || !bastionManagerUrl) return;
  try {
    await fetch(`${bastionManagerUrl}/bastion/heartbeat`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id: bastionId }),
    });
    console.log(`[BastionAgent] Heartbeat sent`);
  } catch (err) {
    console.warn("[BastionAgent] Heartbeat failed:", err);
  }
}

async function autoRegisterBastionAgent() {
  const name = Deno.env.get("BASTION_AGENT_NAME");
  if (!name) {
    console.error(
      "[BastionAgent] BASTION_AGENT_NAME is not set. Registration aborted.",
    );
    Deno.exit(1);
  }

  let ip_address = Deno.env.get("BASTION_AGENT_IP") || null;
  if (!ip_address) {
    ip_address = await detectPublicIP();
    if (!ip_address) {
      console.error(
        "[BastionAgent] No public IP detected and BASTION_AGENT_IP not set. Registration aborted.",
      );
      Deno.exit(1);
    }
  }

  bastionManagerUrl = Deno.env.get("KOLLECTYVE_BACKEND_URL");
  if (!bastionManagerUrl) {
    console.error(
      "[BastionAgent] KOLLECTYVE_BACKEND_URL is not set. Registration aborted.",
    );
    Deno.exit(1);
  }

  let retryCount = 0;
  while (true) {
    try {
      const res = await fetch(`${bastionManagerUrl}/bastions/register`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, ip_address }),
      });
      if (res.ok) {
        const data = await res.json();
        bastionId = data.bastion?.id || data.id || null;
        console.log(
          `[BastionAgent] Registered with manager as ${name} (${ip_address})`,
        );
        break;
      } else {
        const errMsg = await res.text();
        console.error(`[BastionAgent] Registration failed:`, errMsg);
      }
    } catch (err) {
      console.error(`[BastionAgent] Registration error:`, err);
    }
    retryCount++;
    if (retryCount % 10 === 0) {
      console.warn(
        `[BastionAgent] Registration has failed ${retryCount} times. Please check configuration and connectivity.`,
      );
    }
    await new Promise((resolve) => setTimeout(resolve, 30000));
  }

  if (!bastionId) {
    console.error(
      "[BastionAgent] Could not obtain bastion ID after registration. Exiting.",
    );
    Deno.exit(1);
  }

  await restoreTunnels(bastionId, bastionManagerUrl);

  // Heartbeat cron will now have access to bastionId and bastionManagerUrl
  Deno.cron("Bastion Agent Heartbeat", "* * * * *", sendHeartbeat);
}

// Helper to restore tunnels after restart
async function restoreTunnels(bastionId: string, bastionManagerUrl: string) {
  try {
    const res = await fetch(
      `${bastionManagerUrl}/bastions/tunnels-to-restore/${bastionId}`,
    );
    if (!res.ok) {
      console.error(
        `[BastionAgent] Failed to fetch tunnels to restore: ${res.status}`,
      );
      return;
    }
    const tunnels = await res.json();
    if (!Array.isArray(tunnels)) {
      console.warn("[BastionAgent] No tunnels to restore.");
      return;
    }
    for (const tunnel of tunnels) {
      // Compose the SSH command as expected by the /tunnel endpoint
      const ssh_command = tunnel.ssh_command || tunnel.sshCommand;
      const tunnel_id = tunnel.id;
      const requested_port = tunnel.assigned_port || tunnel.port;
      if (!ssh_command || !tunnel_id || !requested_port) {
        console.warn(
          `[BastionAgent] Skipping tunnel restore due to missing info:`,
          tunnel,
        );
        continue;
      }
      // Use the same logic as POST /tunnel
      if (runningTunnels[tunnel_id]) {
        console.log(`[BastionAgent] Tunnel ${tunnel_id} already running`);
        continue;
      }
      try {
        const [cmd, ...args] = [
          "bash",
          "-c",
          ssh_command.replace(/\{PORT\}/g, String(requested_port)),
        ];
        const process = new Deno.Command(cmd, {
          args,
          stdin: "null",
          stdout: "null",
          stderr: "null",
        }).spawn();
        runningTunnels[tunnel_id] = { process, sshCommand: ssh_command };
        usedPorts.add(requested_port);
        console.log(
          `[BastionAgent] Restored tunnel ${tunnel_id} on port ${requested_port}`,
        );
      } catch (e) {
        const errMsg = e instanceof Error ? e.message : String(e);
        console.error(
          `[BastionAgent] Failed to restore tunnel ${tunnel_id}: ${errMsg}`,
        );
      }
    }
  } catch (err) {
    console.error("[BastionAgent] Error restoring tunnels:", err);
  }
}

//autoRegisterBastionAgent();

Deno.serve({ port: 8700, hostname: "0.0.0.0" }, app.fetch);
