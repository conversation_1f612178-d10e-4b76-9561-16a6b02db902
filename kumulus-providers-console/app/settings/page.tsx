"use client"

import { useEffect, useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Settings as SettingsIcon, 
  RefreshCw,
  AlertCircle,
  Save,
  Bell,
  Shield,
  Palette,
  Globe
} from "lucide-react"

interface SettingsData {
  userId: string
  settings: {
    notifications: {
      email: boolean
      push: boolean
      sms: boolean
    }
    privacy: {
      profileVisible: boolean
      activityVisible: boolean
    }
    security: {
      twoFactorEnabled: boolean
      lastPasswordChange: string
    }
    preferences: {
      theme: string
      language: string
      timezone: string
    }
  }
}

export default function SettingsPage() {
  const [settingsData, setSettingsData] = useState<SettingsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<SettingsData["settings"] | null>(null)

  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch("http://localhost:8000/api/profile/settings", {
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to fetch settings")
      }

      const data = await response.json()
      setSettingsData(data)
      setFormData(data.settings)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!formData) return

    try {
      setIsSaving(true)
      setError(null)

      const response = await fetch("http://localhost:8000/api/profile/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        throw new Error("Failed to update settings")
      }

      const data = await response.json()
      setSettingsData({ userId: settingsData?.userId || "", settings: data.settings })
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save settings")
    } finally {
      setIsSaving(false)
    }
  }

  const updateSetting = (section: keyof SettingsData["settings"], key: string, value: any) => {
    if (!formData) return
    setFormData({
      ...formData,
      [section]: {
        ...formData[section],
        [key]: value,
      },
    })
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading settings...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error && !settingsData) {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Settings
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchSettings} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">
              Manage your application preferences and security settings
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchSettings} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={handleSave} disabled={isSaving} size="sm">
              {isSaving ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        </div>

        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <p className="text-sm">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid gap-6 md:grid-cols-2">
          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              <CardDescription>Configure how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Email Notifications</p>
                  <p className="text-xs text-muted-foreground">Receive updates via email</p>
                </div>
                <Button
                  variant={formData?.notifications.email ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateSetting("notifications", "email", !formData?.notifications.email)}
                >
                  {formData?.notifications.email ? "Enabled" : "Disabled"}
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Push Notifications</p>
                  <p className="text-xs text-muted-foreground">Browser push notifications</p>
                </div>
                <Button
                  variant={formData?.notifications.push ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateSetting("notifications", "push", !formData?.notifications.push)}
                >
                  {formData?.notifications.push ? "Enabled" : "Disabled"}
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">SMS Notifications</p>
                  <p className="text-xs text-muted-foreground">Text message alerts</p>
                </div>
                <Button
                  variant={formData?.notifications.sms ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateSetting("notifications", "sms", !formData?.notifications.sms)}
                >
                  {formData?.notifications.sms ? "Enabled" : "Disabled"}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security
              </CardTitle>
              <CardDescription>Security and authentication settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Two-Factor Authentication</p>
                  <p className="text-xs text-muted-foreground">Add an extra layer of security</p>
                </div>
                <Badge variant={formData?.security.twoFactorEnabled ? "default" : "secondary"}>
                  {formData?.security.twoFactorEnabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Last Password Change</p>
                  <p className="text-xs text-muted-foreground">
                    {formData?.security.lastPasswordChange ? 
                      new Date(formData.security.lastPasswordChange).toLocaleDateString() : 
                      "Never"
                    }
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Change Password
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Preferences
              </CardTitle>
              <CardDescription>Customize your experience</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Theme</p>
                  <p className="text-xs text-muted-foreground">Choose your preferred theme</p>
                </div>
                <Badge variant="secondary">{formData?.preferences.theme}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Language</p>
                  <p className="text-xs text-muted-foreground">Interface language</p>
                </div>
                <Badge variant="secondary">{formData?.preferences.language}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Timezone</p>
                  <p className="text-xs text-muted-foreground">Your local timezone</p>
                </div>
                <Badge variant="secondary">{formData?.preferences.timezone}</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Privacy */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Privacy
              </CardTitle>
              <CardDescription>Control your privacy settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Profile Visibility</p>
                  <p className="text-xs text-muted-foreground">Make your profile visible to others</p>
                </div>
                <Button
                  variant={formData?.privacy.profileVisible ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateSetting("privacy", "profileVisible", !formData?.privacy.profileVisible)}
                >
                  {formData?.privacy.profileVisible ? "Public" : "Private"}
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Activity Visibility</p>
                  <p className="text-xs text-muted-foreground">Show your activity to others</p>
                </div>
                <Button
                  variant={formData?.privacy.activityVisible ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateSetting("privacy", "activityVisible", !formData?.privacy.activityVisible)}
                >
                  {formData?.privacy.activityVisible ? "Visible" : "Hidden"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
