import { NextResponse } from 'next/server'
import { mockDataStore } from '@/lib/mock-data'

export async function GET() {
  try {
    const resources = mockDataStore.getAllResources()
    const activeResources = resources.filter(r => r.status === 'active').length
    
    // Mock data for provider dashboard
    const dashboardData = {
      user: {
        id: "provider_1",
        name: "Provider User",
        email: "<EMAIL>"
      },
      stats: {
        totalResources: resources.length,
        activeResources: activeResources,
        totalEarnings: 1247.50,
        utilizationRate: "78%"
      },
      recentActivity: [
        {
          id: 1,
          action: "Resource started providing",
          resource: "My Home PC",
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString() // 15 minutes ago
        },
        {
          id: 2,
          action: "VM allocation completed",
          resource: "Ubuntu Server 1",
          timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString() // 45 minutes ago
        },
        {
          id: 3,
          action: "Resource registered",
          resource: "Development Machine",
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2 hours ago
        },
        {
          id: 4,
          action: "Earnings payout processed",
          resource: "All Resources",
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1 day ago
        }
      ]
    }

    return NextResponse.json(dashboardData)
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to fetch dashboard overview' },
      { status: 500 }
    )
  }
}
