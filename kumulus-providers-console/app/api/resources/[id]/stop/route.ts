import { NextRequest, NextResponse } from 'next/server'
import { mockDataStore } from '@/lib/mock-data'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const resource = mockDataStore.getResourceById(id)

    if (!resource) {
      return NextResponse.json(
        { message: 'Resource not found' },
        { status: 404 }
      )
    }

    // Check if resource is currently active
    if (resource.status !== 'active') {
      return NextResponse.json(
        { message: 'Resource is not currently providing' },
        { status: 400 }
      )
    }

    // Update resource status back to ready
    const updatedResource = mockDataStore.updateResource(id, { status: 'ready' })

    return NextResponse.json({
      message: 'Resource has stopped providing computing resources',
      resource: updatedResource
    })
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to stop providing' },
      { status: 500 }
    )
  }
}
