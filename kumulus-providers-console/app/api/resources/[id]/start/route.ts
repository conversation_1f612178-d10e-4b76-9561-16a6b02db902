import { NextRequest, NextResponse } from 'next/server'
import { mockDataStore } from '@/lib/mock-data'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const resource = mockDataStore.getResourceById(id)

    if (!resource) {
      return NextResponse.json(
        { message: 'Resource not found' },
        { status: 404 }
      )
    }

    // Check if resource is ready to start providing
    if (resource.status !== 'ready') {
      return NextResponse.json(
        { message: 'Resource is not ready to start providing. Please complete the setup first.' },
        { status: 400 }
      )
    }

    // Update resource status to active
    const updatedResource = mockDataStore.updateResource(id, { status: 'active' })

    return NextResponse.json({
      message: 'Resource is now providing computing resources',
      resource: updatedResource
    })
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to start providing' },
      { status: 500 }
    )
  }
}
