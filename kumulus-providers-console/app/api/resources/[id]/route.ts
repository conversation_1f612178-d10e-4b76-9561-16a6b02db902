import { NextRequest, NextResponse } from 'next/server'
import { mockDataStore } from '@/lib/mock-data'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const resource = mockDataStore.getResourceById(id)

    if (!resource) {
      return NextResponse.json(
        { message: 'Resource not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(resource)
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to fetch resource' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    const resource = mockDataStore.getResourceById(id)
    if (!resource) {
      return NextResponse.json(
        { message: 'Resource not found' },
        { status: 404 }
      )
    }

    const updatedResource = mockDataStore.updateResource(id, body)
    return NextResponse.json(updatedResource)
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to update resource' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const deleted = mockDataStore.deleteResource(id)

    if (!deleted) {
      return NextResponse.json(
        { message: 'Resource not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { message: 'Resource deleted successfully' },
      { status: 200 }
    )
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to delete resource' },
      { status: 500 }
    )
  }
}
