import { NextRequest, NextResponse } from 'next/server'
import { mockDataStore, Resource } from '@/lib/mock-data'

export async function GET() {
  try {
    const resources = mockDataStore.getAllResources()
    return NextResponse.json({
      resources: resources,
      total: resources.length
    })
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to fetch resources' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = ['resource_name', 'cpu_cores_offered', 'ram_gb_offered', 'storage_gb_offered']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { message: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // Create new resource
    const newResource: Resource = {
      id: `resource_${Date.now()}`,
      resource_name: body.resource_name,
      cpu_cores_offered: parseInt(body.cpu_cores_offered),
      ram_gb_offered: parseInt(body.ram_gb_offered),
      storage_gb_offered: parseInt(body.storage_gb_offered),
      gpu_available: body.gpu_available || false,
      gpu_count: body.gpu_available ? parseInt(body.gpu_count) || 0 : undefined,
      gpu_vram_gb: body.gpu_available ? parseInt(body.gpu_vram_gb) || 0 : undefined,
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Add to data store
    const createdResource = mockDataStore.addResource(newResource)

    // Simulate installation process after a short delay
    setTimeout(() => {
      mockDataStore.simulateInstallation(createdResource.id)
    }, 1000)

    return NextResponse.json(createdResource, { status: 201 })
  } catch (error) {
    return NextResponse.json(
      { message: 'Failed to create resource' },
      { status: 500 }
    )
  }
}
