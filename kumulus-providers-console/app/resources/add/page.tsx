"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  ArrowRight,
  Server,
  Cpu,
  MemoryStick,
  HardDrive,
  Monitor,
  AlertCircle,
  CheckCircle,
  Loader2,
  Terminal,
  Copy,
  Play,
  RefreshCw,
  Clock
} from "lucide-react"
import Link from "next/link"

interface ResourceFormData {
  resource_name: string
  cpu_cores_offered: number
  ram_gb_offered: number
  storage_gb_offered: number
  gpu_available: boolean
  gpu_count: number
  gpu_vram_gb: number
}

interface CreatedResource {
  id: string
  resource_name: string
  cpu_cores_offered: number
  ram_gb_offered: number
  storage_gb_offered: number
  gpu_available: boolean
  gpu_count?: number
  gpu_vram_gb?: number
  status: 'pending' | 'installing' | 'ready' | 'active' | 'error'
  created_at: string
  updated_at: string
}

export default function AddResourcePage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [createdResource, setCreatedResource] = useState<CreatedResource | null>(null)
  const [copied, setCopied] = useState(false)
  const [formData, setFormData] = useState<ResourceFormData>({
    resource_name: "",
    cpu_cores_offered: 1,
    ram_gb_offered: 1,
    storage_gb_offered: 10,
    gpu_available: false,
    gpu_count: 0,
    gpu_vram_gb: 0
  })

  const installCommand = "curl https://kumulus.cloud/install.sh | sh"

  // Poll for resource status updates
  useEffect(() => {
    if (createdResource && (createdResource.status === 'pending' || createdResource.status === 'installing')) {
      const interval = setInterval(async () => {
        try {
          const response = await fetch(`/api/resources/${createdResource.id}`, {
            credentials: "include",
          })
          if (response.ok) {
            const updatedResource = await response.json()
            setCreatedResource(updatedResource)

            // Move to step 3 when ready
            if (updatedResource.status === 'ready' && currentStep === 2) {
              setCurrentStep(3)
            }
          }
        } catch (error) {
          console.error("Error polling resource status:", error)
        }
      }, 2000)

      return () => clearInterval(interval)
    }
  }, [createdResource, currentStep])

  const handleInputChange = (field: keyof ResourceFormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const copyInstallCommand = () => {
    navigator.clipboard.writeText(installCommand)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    try {
      // Validate form data
      if (!formData.resource_name.trim()) {
        throw new Error("Resource name is required")
      }
      if (formData.cpu_cores_offered < 1) {
        throw new Error("CPU cores must be at least 1")
      }
      if (formData.ram_gb_offered < 1) {
        throw new Error("RAM must be at least 1 GB")
      }
      if (formData.storage_gb_offered < 1) {
        throw new Error("Storage must be at least 1 GB")
      }
      if (formData.gpu_available && formData.gpu_count < 1) {
        throw new Error("GPU count must be at least 1 when GPU is available")
      }
      if (formData.gpu_available && formData.gpu_vram_gb < 1) {
        throw new Error("GPU VRAM must be at least 1 GB when GPU is available")
      }

      const submitData = {
        ...formData,
        // Only include GPU fields if GPU is available
        ...(formData.gpu_available ? {
          gpu_count: formData.gpu_count,
          gpu_vram_gb: formData.gpu_vram_gb
        } : {
          gpu_count: undefined,
          gpu_vram_gb: undefined
        })
      }

      const response = await fetch("/api/resources", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to create resource")
      }

      // Get the created resource data
      const newResource = await response.json()
      setCreatedResource(newResource)

      // Move to step 2
      setCurrentStep(2)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStartProviding = async () => {
    if (!createdResource) return

    try {
      const response = await fetch(`/api/resources/${createdResource.id}/start`, {
        method: "POST",
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to start providing")
      }

      // Update resource status
      const updatedResource = await response.json()
      setCreatedResource(updatedResource.resource)

      // Redirect to resources page
      router.push("/resources")
    } catch (error) {
      console.error("Error starting providing:", error)
      setError("Failed to start providing. Please try again.")
    }
  }

  const getStepStatus = (step: number) => {
    if (step < currentStep) return 'completed'
    if (step === currentStep) return 'current'
    return 'upcoming'
  }

  const getStepIcon = (step: number, status: string) => {
    if (status === 'completed') return <CheckCircle className="h-5 w-5" />
    if (status === 'current') {
      if (step === 2 && createdResource?.status === 'installing') {
        return <RefreshCw className="h-5 w-5 animate-spin" />
      }
      return <div className="w-5 h-5 rounded-full border-2 border-current" />
    }
    return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/resources">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Resources
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight">Add Resource</h1>
            <p className="text-muted-foreground">
              Register a new computing resource to contribute to the network
            </p>
          </div>
        </div>

        {/* Progress Steps */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              {[
                { number: 1, title: "Resource Details", description: "Enter your machine specifications" },
                { number: 2, title: "Install Agent", description: "Run the installation script" },
                { number: 3, title: "Start Providing", description: "Begin earning rewards" }
              ].map((step, index) => {
                const status = getStepStatus(step.number)
                return (
                  <div key={step.number} className="flex items-center">
                    <div className="flex flex-col items-center">
                      <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                        status === 'completed' ? 'bg-green-100 border-green-500 text-green-600' :
                        status === 'current' ? 'bg-blue-100 border-blue-500 text-blue-600' :
                        'bg-gray-100 border-gray-300 text-gray-400'
                      }`}>
                        {getStepIcon(step.number, status)}
                      </div>
                      <div className="mt-2 text-center">
                        <p className={`text-sm font-medium ${
                          status === 'current' ? 'text-blue-600' :
                          status === 'completed' ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {step.title}
                        </p>
                        <p className="text-xs text-muted-foreground">{step.description}</p>
                      </div>
                    </div>
                    {index < 2 && (
                      <div className={`flex-1 h-0.5 mx-4 ${
                        getStepStatus(step.number + 1) !== 'upcoming' ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                    )}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Step 1: Resource Information
              </CardTitle>
              <CardDescription>
                Provide details about the computing resources you want to offer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
              {/* Resource Name */}
              <div className="space-y-2">
                <Label htmlFor="resource_name">Resource Name</Label>
                <Input
                  id="resource_name"
                  type="text"
                  placeholder="e.g., My Home PC, Ubuntu Server 1"
                  value={formData.resource_name}
                  onChange={(e) => handleInputChange("resource_name", e.target.value)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  A friendly name to identify your machine on the platform
                </p>
              </div>

              {/* CPU Cores */}
              <div className="space-y-2">
                <Label htmlFor="cpu_cores_offered" className="flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  CPU Cores to Offer
                </Label>
                <Input
                  id="cpu_cores_offered"
                  type="number"
                  min="1"
                  value={formData.cpu_cores_offered}
                  onChange={(e) => handleInputChange("cpu_cores_offered", parseInt(e.target.value) || 1)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Number of CPU cores you're willing to dedicate for running user VMs
                </p>
              </div>

              {/* RAM */}
              <div className="space-y-2">
                <Label htmlFor="ram_gb_offered" className="flex items-center gap-2">
                  <MemoryStick className="h-4 w-4" />
                  RAM to Offer (GB)
                </Label>
                <Input
                  id="ram_gb_offered"
                  type="number"
                  min="1"
                  value={formData.ram_gb_offered}
                  onChange={(e) => handleInputChange("ram_gb_offered", parseInt(e.target.value) || 1)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Amount of RAM in GB you're willing to dedicate
                </p>
              </div>

              {/* Storage */}
              <div className="space-y-2">
                <Label htmlFor="storage_gb_offered" className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  Storage to Offer (GB)
                </Label>
                <Input
                  id="storage_gb_offered"
                  type="number"
                  min="1"
                  value={formData.storage_gb_offered}
                  onChange={(e) => handleInputChange("storage_gb_offered", parseInt(e.target.value) || 1)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Amount of disk space in GB you're willing to dedicate for user VMs
                </p>
              </div>

              {/* GPU Section */}
              <div className="space-y-4 p-4 border rounded-lg">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="gpu_available"
                    checked={formData.gpu_available}
                    onChange={(e) => handleInputChange("gpu_available", e.target.checked)}
                  />
                  <Label htmlFor="gpu_available" className="flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    This machine has GPU(s) available
                  </Label>
                </div>

                {formData.gpu_available && (
                  <div className="space-y-4 ml-6">
                    <div className="space-y-2">
                      <Label htmlFor="gpu_count">Number of GPUs</Label>
                      <Input
                        id="gpu_count"
                        type="number"
                        min="1"
                        value={formData.gpu_count}
                        onChange={(e) => handleInputChange("gpu_count", parseInt(e.target.value) || 1)}
                        required={formData.gpu_available}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gpu_vram_gb">GPU VRAM (GB)</Label>
                      <Input
                        id="gpu_vram_gb"
                        type="number"
                        min="1"
                        value={formData.gpu_vram_gb}
                        onChange={(e) => handleInputChange("gpu_vram_gb", parseInt(e.target.value) || 1)}
                        required={formData.gpu_available}
                      />
                      <p className="text-xs text-muted-foreground">
                        Total VRAM of one GPU (or sum if multiple identical GPUs)
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Error Display */}
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Submit Button */}
              <div className="flex gap-3">
                <Button type="submit" disabled={isSubmitting} className="flex-1">
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Resource...
                    </>
                  ) : (
                    <>
                      <ArrowRight className="h-4 w-4 mr-2" />
                      Continue to Installation
                    </>
                  )}
                </Button>
                <Link href="/resources">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>
        )}

        {/* Step 2: Installation */}
        {currentStep === 2 && createdResource && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Terminal className="h-5 w-5" />
                Step 2: Install Kumulus Agent
              </CardTitle>
              <CardDescription>
                Run the installation script on your machine to set up the Kumulus agent
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Resource Summary */}
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">✅ Resource Registered Successfully</h4>
                <p className="text-sm text-green-700 mb-3">
                  <strong>{createdResource.resource_name}</strong> has been registered with the following specifications:
                </p>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4" />
                    <span>{createdResource.cpu_cores_offered} CPU cores</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MemoryStick className="h-4 w-4" />
                    <span>{createdResource.ram_gb_offered} GB RAM</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4" />
                    <span>{createdResource.storage_gb_offered} GB storage</span>
                  </div>
                  {createdResource.gpu_available && (
                    <div className="flex items-center gap-2">
                      <Monitor className="h-4 w-4" />
                      <span>{createdResource.gpu_count} GPU(s)</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Installation Instructions */}
              <div className="space-y-4">
                <h4 className="font-semibold">Installation Instructions</h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                      1
                    </div>
                    <div>
                      <p className="font-medium">Open Terminal on Your Machine</p>
                      <p className="text-sm text-muted-foreground">
                        Access the command line interface on the machine you want to register.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                      2
                    </div>
                    <div className="flex-1">
                      <p className="font-medium mb-2">Run the Installation Command</p>
                      <div className="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm flex items-center justify-between">
                        <code>{installCommand}</code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={copyInstallCommand}
                          className="text-green-400 hover:text-green-300"
                        >
                          {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        The script will automatically detect your system and install the agent.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Display */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  {createdResource.status === 'pending' && (
                    <>
                      <Clock className="h-5 w-5 text-yellow-600" />
                      <div>
                        <p className="font-medium text-yellow-800">Waiting for Installation</p>
                        <p className="text-sm text-yellow-700">
                          Run the command above to start the installation process.
                        </p>
                      </div>
                    </>
                  )}
                  {createdResource.status === 'installing' && (
                    <>
                      <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
                      <div>
                        <p className="font-medium text-blue-800">Installation in Progress</p>
                        <p className="text-sm text-blue-700">
                          The Kumulus agent is being installed and configured. This may take a few minutes.
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Start Providing */}
        {currentStep === 3 && createdResource && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Step 3: Start Providing Resources
              </CardTitle>
              <CardDescription>
                Your machine is ready! Start providing computing resources to earn rewards.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Success Message */}
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">🎉 Setup Complete!</h4>
                <p className="text-sm text-green-700">
                  The Kumulus agent has been successfully installed and configured on your machine.
                  Your resource is now ready to start providing computing power to the network.
                </p>
              </div>

              {/* What Happens Next */}
              <div className="space-y-4">
                <h4 className="font-semibold">What happens when you start providing?</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-3 border rounded-lg">
                    <h5 className="font-medium mb-1">Resource Allocation</h5>
                    <p className="text-sm text-muted-foreground">
                      Your machine will be available for users to rent computing resources.
                    </p>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <h5 className="font-medium mb-1">Earn Rewards</h5>
                    <p className="text-sm text-muted-foreground">
                      You'll earn tokens for every hour your resources are actively used.
                    </p>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <h5 className="font-medium mb-1">Automatic Management</h5>
                    <p className="text-sm text-muted-foreground">
                      The agent handles all VM creation and management automatically.
                    </p>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <h5 className="font-medium mb-1">Stop Anytime</h5>
                    <p className="text-sm text-muted-foreground">
                      You can stop providing resources at any time from your dashboard.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button onClick={handleStartProviding} className="flex-1">
                  <Play className="h-4 w-4 mr-2" />
                  Start Providing Now
                </Button>
                <Link href="/resources">
                  <Button variant="outline">
                    Start Later
                  </Button>
                </Link>
              </div>

              {/* Error Display */}
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
