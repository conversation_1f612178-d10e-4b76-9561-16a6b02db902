"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  ArrowLeft,
  Server,
  Cpu,
  MemoryStick,
  HardDrive,
  Monitor,
  AlertCircle,
  CheckCircle,
  Loader2
} from "lucide-react"
import Link from "next/link"

interface ResourceFormData {
  resource_name: string
  cpu_cores_offered: number
  ram_gb_offered: number
  storage_gb_offered: number
  gpu_available: boolean
  gpu_count: number
  gpu_vram_gb: number
}

export default function AddResourcePage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<ResourceFormData>({
    resource_name: "",
    cpu_cores_offered: 1,
    ram_gb_offered: 1,
    storage_gb_offered: 10,
    gpu_available: false,
    gpu_count: 0,
    gpu_vram_gb: 0
  })

  const handleInputChange = (field: keyof ResourceFormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    try {
      // Validate form data
      if (!formData.resource_name.trim()) {
        throw new Error("Resource name is required")
      }
      if (formData.cpu_cores_offered < 1) {
        throw new Error("CPU cores must be at least 1")
      }
      if (formData.ram_gb_offered < 1) {
        throw new Error("RAM must be at least 1 GB")
      }
      if (formData.storage_gb_offered < 1) {
        throw new Error("Storage must be at least 1 GB")
      }
      if (formData.gpu_available && formData.gpu_count < 1) {
        throw new Error("GPU count must be at least 1 when GPU is available")
      }
      if (formData.gpu_available && formData.gpu_vram_gb < 1) {
        throw new Error("GPU VRAM must be at least 1 GB when GPU is available")
      }

      const submitData = {
        ...formData,
        // Only include GPU fields if GPU is available
        ...(formData.gpu_available ? {
          gpu_count: formData.gpu_count,
          gpu_vram_gb: formData.gpu_vram_gb
        } : {
          gpu_count: undefined,
          gpu_vram_gb: undefined
        })
      }

      const response = await fetch("/api/resources", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to create resource")
      }

      // Get the created resource data
      const createdResource = await response.json()

      // Redirect to success page with resource info
      router.push(`/resources/success?id=${createdResource.id}&name=${encodeURIComponent(createdResource.resource_name)}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-2xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/resources">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Resources
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Add Resource</h1>
            <p className="text-muted-foreground">
              Register a new computing resource to contribute to the network
            </p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Resource Information
            </CardTitle>
            <CardDescription>
              Provide details about the computing resources you want to offer
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Resource Name */}
              <div className="space-y-2">
                <Label htmlFor="resource_name">Resource Name</Label>
                <Input
                  id="resource_name"
                  type="text"
                  placeholder="e.g., My Home PC, Ubuntu Server 1"
                  value={formData.resource_name}
                  onChange={(e) => handleInputChange("resource_name", e.target.value)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  A friendly name to identify your machine on the platform
                </p>
              </div>

              {/* CPU Cores */}
              <div className="space-y-2">
                <Label htmlFor="cpu_cores_offered" className="flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  CPU Cores to Offer
                </Label>
                <Input
                  id="cpu_cores_offered"
                  type="number"
                  min="1"
                  value={formData.cpu_cores_offered}
                  onChange={(e) => handleInputChange("cpu_cores_offered", parseInt(e.target.value) || 1)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Number of CPU cores you're willing to dedicate for running user VMs
                </p>
              </div>

              {/* RAM */}
              <div className="space-y-2">
                <Label htmlFor="ram_gb_offered" className="flex items-center gap-2">
                  <MemoryStick className="h-4 w-4" />
                  RAM to Offer (GB)
                </Label>
                <Input
                  id="ram_gb_offered"
                  type="number"
                  min="1"
                  value={formData.ram_gb_offered}
                  onChange={(e) => handleInputChange("ram_gb_offered", parseInt(e.target.value) || 1)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Amount of RAM in GB you're willing to dedicate
                </p>
              </div>

              {/* Storage */}
              <div className="space-y-2">
                <Label htmlFor="storage_gb_offered" className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  Storage to Offer (GB)
                </Label>
                <Input
                  id="storage_gb_offered"
                  type="number"
                  min="1"
                  value={formData.storage_gb_offered}
                  onChange={(e) => handleInputChange("storage_gb_offered", parseInt(e.target.value) || 1)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Amount of disk space in GB you're willing to dedicate for user VMs
                </p>
              </div>

              {/* GPU Section */}
              <div className="space-y-4 p-4 border rounded-lg">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="gpu_available"
                    checked={formData.gpu_available}
                    onChange={(e) => handleInputChange("gpu_available", e.target.checked)}
                  />
                  <Label htmlFor="gpu_available" className="flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    This machine has GPU(s) available
                  </Label>
                </div>

                {formData.gpu_available && (
                  <div className="space-y-4 ml-6">
                    <div className="space-y-2">
                      <Label htmlFor="gpu_count">Number of GPUs</Label>
                      <Input
                        id="gpu_count"
                        type="number"
                        min="1"
                        value={formData.gpu_count}
                        onChange={(e) => handleInputChange("gpu_count", parseInt(e.target.value) || 1)}
                        required={formData.gpu_available}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gpu_vram_gb">GPU VRAM (GB)</Label>
                      <Input
                        id="gpu_vram_gb"
                        type="number"
                        min="1"
                        value={formData.gpu_vram_gb}
                        onChange={(e) => handleInputChange("gpu_vram_gb", parseInt(e.target.value) || 1)}
                        required={formData.gpu_available}
                      />
                      <p className="text-xs text-muted-foreground">
                        Total VRAM of one GPU (or sum if multiple identical GPUs)
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Error Display */}
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Submit Button */}
              <div className="flex gap-3">
                <Button type="submit" disabled={isSubmitting} className="flex-1">
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Resource...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Create Resource
                    </>
                  )}
                </Button>
                <Link href="/resources">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
