"use client"

import { useEffect, useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Server, 
  Plus, 
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Cpu,
  HardDrive,
  MemoryStick,
  Monitor
} from "lucide-react"
import Link from "next/link"

interface Resource {
  id: string
  resource_name: string
  cpu_cores_offered: number
  ram_gb_offered: number
  storage_gb_offered: number
  gpu_available: boolean
  gpu_count?: number
  gpu_vram_gb?: number
  status: 'pending' | 'installing' | 'ready' | 'active' | 'error'
  created_at: string
  updated_at: string
}

interface ResourcesData {
  resources: Resource[]
  total: number
}

export default function ResourcesPage() {
  const [resourcesData, setResourcesData] = useState<ResourcesData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchResourcesData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch("/api/resources", {
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to fetch resources data")
      }

      const data = await response.json()
      setResourcesData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchResourcesData()
  }, [])

  const getStatusBadge = (status: Resource['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Step 1: Pending Setup</Badge>
      case 'installing':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800"><RefreshCw className="h-3 w-3 mr-1 animate-spin" />Step 2: Installing</Badge>
      case 'ready':
        return <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Step 3: Ready to Provide</Badge>
      case 'active':
        return <Badge variant="default" className="bg-green-600"><CheckCircle className="h-3 w-3 mr-1" />Active - Providing</Badge>
      case 'error':
        return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Error</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const handleStartProviding = async (resourceId: string) => {
    try {
      const response = await fetch(`/api/resources/${resourceId}/start`, {
        method: "POST",
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to start providing")
      }

      // Refresh the resources data
      fetchResourcesData()
    } catch (error) {
      console.error("Error starting providing:", error)
    }
  }

  const handleStopProviding = async (resourceId: string) => {
    try {
      const response = await fetch(`/api/resources/${resourceId}/stop`, {
        method: "POST",
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to stop providing")
      }

      // Refresh the resources data
      fetchResourcesData()
    } catch (error) {
      console.error("Error stopping providing:", error)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading resources...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Resources
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchResourcesData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </DashboardLayout>
    )
  }

  // No resources state
  if (!resourcesData?.resources || resourcesData.resources.length === 0) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Resources</h1>
              <p className="text-muted-foreground">
                Manage your computing resources and infrastructure
              </p>
            </div>
            <Button onClick={fetchResourcesData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Empty State */}
          <Card className="text-center py-12">
            <CardContent>
              <Server className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No resources registered</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Get started by adding your first computing resource. You can register servers, 
                workstations, or any machine you want to contribute to the network.
              </p>
              <Link href="/resources/add">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Resource
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  // Resources list state
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Resources</h1>
            <p className="text-muted-foreground">
              {resourcesData.total} resource{resourcesData.total !== 1 ? 's' : ''} registered
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchResourcesData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Link href="/resources/add">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Resource
              </Button>
            </Link>
          </div>
        </div>

        {/* Resources Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {resourcesData.resources.map((resource) => (
            <Card key={resource.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{resource.resource_name}</CardTitle>
                  {getStatusBadge(resource.status)}
                </div>
                <CardDescription>
                  Added {new Date(resource.created_at).toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <Cpu className="h-4 w-4 text-muted-foreground" />
                    <span>{resource.cpu_cores_offered} CPU cores</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <MemoryStick className="h-4 w-4 text-muted-foreground" />
                    <span>{resource.ram_gb_offered} GB RAM</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <HardDrive className="h-4 w-4 text-muted-foreground" />
                    <span>{resource.storage_gb_offered} GB storage</span>
                  </div>
                  {resource.gpu_available && (
                    <div className="flex items-center gap-2 text-sm">
                      <Monitor className="h-4 w-4 text-muted-foreground" />
                      <span>{resource.gpu_count} GPU{resource.gpu_count !== 1 ? 's' : ''} ({resource.gpu_vram_gb} GB VRAM)</span>
                    </div>
                  )}
                </div>

                {/* Step-by-step process */}
                <div className="mt-4 space-y-3">
                  {resource.status === 'pending' && (
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                      <h4 className="font-semibold text-yellow-800 mb-2">Step 2: Install Kumulus Agent</h4>
                      <p className="text-sm text-yellow-700 mb-3">
                        Run this command on your machine to install the Kumulus agent:
                      </p>
                      <div className="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm mb-3">
                        curl https://kumulus.cloud/install.sh | sh
                      </div>
                      <p className="text-xs text-yellow-600">
                        The installation script will automatically configure your machine and connect it to the network.
                      </p>
                      <div className="mt-3">
                        <Link href={`/resources/${resource.id}`}>
                          <Button variant="outline" size="sm" className="w-full">
                            View Setup Instructions
                          </Button>
                        </Link>
                      </div>
                    </div>
                  )}

                  {resource.status === 'installing' && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                      <h4 className="font-semibold text-blue-800 mb-2">Step 2: Installation in Progress</h4>
                      <p className="text-sm text-blue-700">
                        The Kumulus agent is being installed and configured on your machine. This may take a few minutes.
                      </p>
                    </div>
                  )}

                  {resource.status === 'ready' && (
                    <div className="space-y-3">
                      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                        <h4 className="font-semibold text-green-800 mb-2">Step 3: Ready to Provide</h4>
                        <p className="text-sm text-green-700">
                          Your machine is successfully configured and ready to start providing computing resources.
                        </p>
                      </div>
                      <Button
                        className="w-full"
                        size="sm"
                        onClick={() => handleStartProviding(resource.id)}
                      >
                        Start Providing
                      </Button>
                    </div>
                  )}

                  {resource.status === 'active' && (
                    <div className="space-y-3">
                      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                        <h4 className="font-semibold text-green-800 mb-2">Currently Providing</h4>
                        <p className="text-sm text-green-700">
                          Your machine is actively providing computing resources to the network.
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        className="w-full"
                        size="sm"
                        onClick={() => handleStopProviding(resource.id)}
                      >
                        Stop Providing
                      </Button>
                    </div>
                  )}

                  {resource.status === 'error' && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                      <h4 className="font-semibold text-red-800 mb-2">Setup Error</h4>
                      <p className="text-sm text-red-700 mb-3">
                        There was an issue setting up your machine. Please check the installation logs or try again.
                      </p>
                      <Link href={`/resources/${resource.id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          View Details & Retry
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </DashboardLayout>
  )
}
