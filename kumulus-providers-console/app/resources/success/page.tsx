"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  CheckCircle,
  ArrowRight,
  Terminal,
  Copy,
  Download,
  ExternalLink
} from "lucide-react"
import Link from "next/link"

export default function ResourceSuccessPage() {
  const searchParams = useSearchParams()
  const resourceId = searchParams.get('id')
  const resourceName = searchParams.get('name')
  const [copied, setCopied] = useState(false)

  const installCommand = "curl https://kumulus.cloud/install.sh | sh"

  const copyInstallCommand = () => {
    navigator.clipboard.writeText(installCommand)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-3xl mx-auto">
        {/* Success Header */}
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight text-green-800">Resource Created Successfully!</h1>
          <p className="text-muted-foreground mt-2">
            {resourceName ? `"${resourceName}"` : 'Your resource'} has been registered with Kumulus
          </p>
        </div>

        {/* Next Steps */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="h-5 w-5" />
              Next Steps: Install Kumulus Agent
            </CardTitle>
            <CardDescription>
              Follow these steps to complete the setup and start providing computing resources
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Step 1 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-semibold">
                1
              </div>
              <div className="flex-1">
                <h3 className="font-semibold mb-2">Open Terminal on Your Machine</h3>
                <p className="text-sm text-muted-foreground">
                  Access the command line interface on the machine you want to register as a resource provider.
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-semibold">
                2
              </div>
              <div className="flex-1">
                <h3 className="font-semibold mb-2">Run the Installation Command</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Copy and paste the following command to install the Kumulus agent:
                </p>
                <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm flex items-center justify-between">
                  <code>{installCommand}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={copyInstallCommand}
                    className="text-green-400 hover:text-green-300"
                  >
                    {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  The installation script will automatically detect your system and configure the agent.
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-semibold">
                3
              </div>
              <div className="flex-1">
                <h3 className="font-semibold mb-2">Wait for Setup Completion</h3>
                <p className="text-sm text-muted-foreground">
                  The installation process will take a few minutes. Once complete, your resource status will automatically update to "Ready to Provide".
                </p>
              </div>
            </div>

            {/* Step 4 */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center font-semibold">
                4
              </div>
              <div className="flex-1">
                <h3 className="font-semibold mb-2">Start Providing Resources</h3>
                <p className="text-sm text-muted-foreground">
                  Once the setup is complete, you can start providing computing resources to the network with a single click.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle>What Happens Next?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">Automatic Configuration</h4>
                <p className="text-sm text-muted-foreground">
                  The agent will automatically configure your machine with the necessary security settings and network connections.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">Resource Verification</h4>
                <p className="text-sm text-muted-foreground">
                  We'll verify that your machine meets the specified resource requirements and is ready to host virtual machines.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">Network Integration</h4>
                <p className="text-sm text-muted-foreground">
                  Your machine will be integrated into the Kumulus network and made available for resource allocation.
                </p>
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-semibold mb-2">Start Earning</h4>
                <p className="text-sm text-muted-foreground">
                  Once active, you'll start earning rewards for providing computing resources to users on the network.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          {resourceId && (
            <Link href={`/resources/${resourceId}`}>
              <Button>
                <ArrowRight className="h-4 w-4 mr-2" />
                View Resource Details
              </Button>
            </Link>
          )}
          <Link href="/resources">
            <Button variant="outline">
              Back to Resources
            </Button>
          </Link>
          <Button variant="outline" asChild>
            <a href="https://docs.kumulus.cloud/setup" target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              Setup Documentation
            </a>
          </Button>
        </div>
      </div>
    </DashboardLayout>
  )
}
