"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft,
  Server,
  Cpu,
  MemoryStick,
  HardDrive,
  Monitor,
  CheckCircle,
  Clock,
  RefreshCw,
  AlertCircle,
  Copy,
  Terminal,
  Download,
  Play,
  Square
} from "lucide-react"
import Link from "next/link"

interface Resource {
  id: string
  resource_name: string
  cpu_cores_offered: number
  ram_gb_offered: number
  storage_gb_offered: number
  gpu_available: boolean
  gpu_count?: number
  gpu_vram_gb?: number
  status: 'pending' | 'installing' | 'ready' | 'active' | 'error'
  created_at: string
  updated_at: string
}

export default function ResourceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [resource, setResource] = useState<Resource | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)

  const resourceId = params.id as string

  const fetchResource = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/resources/${resourceId}`, {
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to fetch resource")
      }

      const data = await response.json()
      setResource(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchResource()
  }, [resourceId])

  const copyInstallCommand = () => {
    const command = "curl https://kumulus.cloud/install.sh | sh"
    navigator.clipboard.writeText(command)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const handleStartProviding = async () => {
    try {
      const response = await fetch(`/api/resources/${resourceId}/start`, {
        method: "POST",
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to start providing")
      }

      fetchResource()
    } catch (error) {
      console.error("Error starting providing:", error)
    }
  }

  const handleStopProviding = async () => {
    try {
      const response = await fetch(`/api/resources/${resourceId}/stop`, {
        method: "POST",
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error("Failed to stop providing")
      }

      fetchResource()
    } catch (error) {
      console.error("Error stopping providing:", error)
    }
  }

  const getStatusInfo = (status: Resource['status']) => {
    switch (status) {
      case 'pending':
        return {
          badge: <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Step 1: Pending Setup</Badge>,
          step: 1,
          title: "Resource Registered",
          description: "Your resource has been registered. Now install the Kumulus agent."
        }
      case 'installing':
        return {
          badge: <Badge variant="secondary" className="bg-blue-100 text-blue-800"><RefreshCw className="h-3 w-3 mr-1 animate-spin" />Step 2: Installing</Badge>,
          step: 2,
          title: "Installing Agent",
          description: "The Kumulus agent is being installed and configured."
        }
      case 'ready':
        return {
          badge: <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Step 3: Ready to Provide</Badge>,
          step: 3,
          title: "Ready to Provide",
          description: "Your machine is configured and ready to start providing resources."
        }
      case 'active':
        return {
          badge: <Badge variant="default" className="bg-green-600"><CheckCircle className="h-3 w-3 mr-1" />Active - Providing</Badge>,
          step: 4,
          title: "Currently Providing",
          description: "Your machine is actively providing computing resources."
        }
      case 'error':
        return {
          badge: <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Error</Badge>,
          step: 0,
          title: "Setup Error",
          description: "There was an issue during setup. Please check the logs and try again."
        }
      default:
        return {
          badge: <Badge variant="secondary">Unknown</Badge>,
          step: 0,
          title: "Unknown Status",
          description: "The resource status is unknown."
        }
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading resource...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error || !resource) {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Resource
            </CardTitle>
            <CardDescription>{error || "Resource not found"}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button onClick={fetchResource} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Link href="/resources">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Resources
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </DashboardLayout>
    )
  }

  const statusInfo = getStatusInfo(resource.status)

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/resources">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Resources
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight">{resource.resource_name}</h1>
            <p className="text-muted-foreground">
              Resource ID: {resource.id}
            </p>
          </div>
          <div>
            {statusInfo.badge}
          </div>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Resource Specifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Specifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Cpu className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{resource.cpu_cores_offered} CPU cores</span>
              </div>
              <div className="flex items-center gap-2">
                <MemoryStick className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{resource.ram_gb_offered} GB RAM</span>
              </div>
              <div className="flex items-center gap-2">
                <HardDrive className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{resource.storage_gb_offered} GB storage</span>
              </div>
              {resource.gpu_available && (
                <div className="flex items-center gap-2">
                  <Monitor className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{resource.gpu_count} GPU{resource.gpu_count !== 1 ? 's' : ''} ({resource.gpu_vram_gb} GB VRAM)</span>
                </div>
              )}
              <div className="pt-2 border-t">
                <p className="text-xs text-muted-foreground">
                  Registered: {new Date(resource.created_at).toLocaleString()}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Setup Progress */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Setup Progress</CardTitle>
              <CardDescription>Follow these steps to get your resource online</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Step 1: Registration */}
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-green-800">Step 1: Resource Registration</h3>
                    <p className="text-sm text-muted-foreground">Resource successfully registered with Kumulus</p>
                  </div>
                </div>

                {/* Step 2: Installation */}
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      statusInfo.step >= 2 
                        ? 'bg-green-100 text-green-600' 
                        : statusInfo.step === 1 
                        ? 'bg-blue-100 text-blue-600' 
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      {statusInfo.step >= 2 ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : statusInfo.step === 1 ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Terminal className="h-4 w-4" />
                      )}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-semibold ${
                      statusInfo.step >= 2 ? 'text-green-800' : 
                      statusInfo.step === 1 ? 'text-blue-800' : 'text-gray-600'
                    }`}>
                      Step 2: Install Kumulus Agent
                    </h3>
                    {resource.status === 'pending' && (
                      <div className="mt-2 space-y-3">
                        <p className="text-sm text-muted-foreground">
                          Run this command on your machine to install the Kumulus agent:
                        </p>
                        <div className="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm flex items-center justify-between">
                          <code>curl https://kumulus.cloud/install.sh | sh</code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={copyInstallCommand}
                            className="text-green-400 hover:text-green-300"
                          >
                            {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          The script will automatically detect your system and install the appropriate agent.
                        </p>
                      </div>
                    )}
                    {resource.status === 'installing' && (
                      <p className="text-sm text-blue-700 mt-1">Installation in progress...</p>
                    )}
                    {statusInfo.step >= 2 && resource.status !== 'installing' && (
                      <p className="text-sm text-green-700 mt-1">Agent successfully installed and configured</p>
                    )}
                  </div>
                </div>

                {/* Step 3: Ready to Provide */}
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      statusInfo.step >= 3 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      {statusInfo.step >= 3 ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className={`font-semibold ${
                      statusInfo.step >= 3 ? 'text-green-800' : 'text-gray-600'
                    }`}>
                      Step 3: Start Providing
                    </h3>
                    {resource.status === 'ready' && (
                      <div className="mt-2 space-y-3">
                        <p className="text-sm text-muted-foreground">
                          Your machine is ready! Click the button below to start providing computing resources.
                        </p>
                        <Button onClick={handleStartProviding} className="w-full">
                          <Play className="h-4 w-4 mr-2" />
                          Start Providing
                        </Button>
                      </div>
                    )}
                    {resource.status === 'active' && (
                      <div className="mt-2 space-y-3">
                        <p className="text-sm text-green-700">
                          Your machine is actively providing computing resources to the network.
                        </p>
                        <Button onClick={handleStopProviding} variant="outline" className="w-full">
                          <Square className="h-4 w-4 mr-2" />
                          Stop Providing
                        </Button>
                      </div>
                    )}
                    {statusInfo.step < 3 && resource.status !== 'ready' && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Complete the previous steps to enable providing
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
