import { createAuthClient } from "better-auth/react";
import { useState, useEffect } from "react";

export const authClient = createAuthClient({
  baseURL: process.env.BACKEND_URL || "http://localhost:8000"  // your Deno server URL
});

// Mock session for development
const mockSession = {
  user: {
    id: "dev-user-1",
    name: "Development User",
    email: "<EMAIL>"
  },
  session: {
    id: "dev-session-1",
    userId: "dev-user-1",
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  }
};

// Override useSession for development
export const useSession = () => {
  const [session, setSession] = useState(mockSession);
  const [isLoading, setIsLoading] = useState(false);

  return {
    data: session,
    isLoading,
    error: null
  };
};

export const { signUp, signIn, signOut } = authClient;
