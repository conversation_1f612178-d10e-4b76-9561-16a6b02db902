#!/bin/bash
set -e
trap 'echo "❌ An error occurred. Exiting..."; exit 1;' ERR

exec > >(tee -i /var/log/docker_install.log) 2>&1

# Provider Status Reporting Configuration
KUMULUS_API_URL="${KUMULUS_API_URL:-http://localhost:8000}"
PROVIDER_ID="${PROVIDER_ID:-}"
PROVIDER_TOKEN="${PROVIDER_TOKEN:-}"
RESOURCE_ID="${RESOURCE_ID:-}"

# Installation Configuration
KUMULUS_USER="kumulus"
KUMULUS_HOME="/opt/kumulus"
KUMULUS_CONFIG="/etc/kumulus"
KUMULUS_LOGS="/var/log/kumulus"
SERVICE_NAME="kumulus-agent"
INSTALL_METHOD="${INSTALL_METHOD:-docker}" # docker or binary

# Function to report installation progress
report_progress() {
    local step="$1"
    local status="$2"
    local progress="$3"
    local message="$4"
    
    if [ -n "$PROVIDER_ID" ] && [ -n "$KUMULUS_API_URL" ]; then
        echo "📊 Reporting progress: $step - $status ($progress%)"
        
        curl -s -X POST "$KUMULUS_API_URL/api/providers/$PROVIDER_ID/installation" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $PROVIDER_TOKEN" \
            -d "{
                \"step\": \"$step\",
                \"status\": \"$status\",
                \"progress\": $progress,
                \"message\": \"$message\",
                \"timestamp\": \"$(date -Iseconds)\"
            }" || echo "⚠️ Failed to report progress (continuing anyway)"
    fi
}

# Function to handle errors with reporting
handle_error() {
    local step="$1"
    local error_message="$2"
    
    echo "❌ ERROR: $error_message"
    report_progress "$step" "failed" 0 "$error_message"
    exit 1
}

# Check system requirements
check_system_requirements() {
    echo "🔍 Checking system requirements..."
    report_progress "system_check" "in_progress" 5 "Checking system requirements"

    # Check OS
    if [[ ! -f /etc/os-release ]]; then
        handle_error "system_check" "Unsupported operating system"
    fi

    source /etc/os-release
    echo "📋 Detected OS: $PRETTY_NAME"

    # Check architecture
    ARCH=$(uname -m)
    if [[ "$ARCH" != "x86_64" && "$ARCH" != "aarch64" ]]; then
        handle_error "system_check" "Unsupported architecture: $ARCH"
    fi

    # Check available memory (minimum 1GB)
    MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    MEMORY_GB=$((MEMORY_KB / 1024 / 1024))
    if [[ $MEMORY_GB -lt 1 ]]; then
        handle_error "system_check" "Insufficient memory. Minimum 1GB required, found ${MEMORY_GB}GB"
    fi

    # Check available disk space (minimum 10GB)
    DISK_SPACE=$(df / | awk 'NR==2 {print $4}')
    DISK_SPACE_GB=$((DISK_SPACE / 1024 / 1024))
    if [[ $DISK_SPACE_GB -lt 10 ]]; then
        handle_error "system_check" "Insufficient disk space. Minimum 10GB required, found ${DISK_SPACE_GB}GB"
    fi

    echo "✅ System requirements check passed"
    report_progress "system_check" "completed" 8 "System requirements verified"
}

# Setup system user and directories
setup_system() {
    echo "🔧 Setting up system user and directories..."
    report_progress "system_setup" "in_progress" 12 "Setting up system directories"

    # Create kumulus user if it doesn't exist
    if ! id "$KUMULUS_USER" &>/dev/null; then
        sudo useradd -r -s /bin/false -d "$KUMULUS_HOME" "$KUMULUS_USER"
        echo "👤 Created user: $KUMULUS_USER"
    fi

    # Create directories
    sudo mkdir -p "$KUMULUS_HOME" "$KUMULUS_CONFIG/keys" "$KUMULUS_LOGS"

    # Set permissions
    sudo chown -R "$KUMULUS_USER:$KUMULUS_USER" "$KUMULUS_HOME" "$KUMULUS_CONFIG" "$KUMULUS_LOGS"
    sudo chmod 700 "$KUMULUS_CONFIG/keys"
    sudo chmod 755 "$KUMULUS_LOGS"

    echo "✅ System setup completed"
    report_progress "system_setup" "completed" 15 "System directories configured"
}

# Check if the script is run as root or with sudo privileges
if [ "$(id -u)" -eq 0 ]; then
    echo "⚠️ Warning: This script is running as root. Proceeding..."
else
    echo "🔒 Running as a non-root user. Sudo privileges are required for installation."
    if ! sudo -v; then
        handle_error "permission_check" "You must have sudo privileges to run this script."
    fi
fi

# Ensure safe Git directory and correct ownership
INSTALL_DIR="/opt/kumulus-provider"
if [ -d "$INSTALL_DIR" ]; then
    git config --global --add safe.directory "$INSTALL_DIR"
    chown -R $(logname):$(logname) "$INSTALL_DIR"
fi

REPO_URL="https://github.com/kollectyve-labs/kumulus-provider.git"

echo "🚀 Starting Provider Setup..."
report_progress "setup_start" "in_progress" 0 "Starting provider setup"

# Run system checks
check_system_requirements
setup_system

# Check if Docker is already installed
report_progress "docker_check" "in_progress" 10 "Checking Docker installation"
if command -v docker &>/dev/null; then
    echo "✅ Docker is already installed."
    docker --version
    report_progress "docker_check" "completed" 20 "Docker already available"
else
    echo "🔄 Docker is not installed. Installing Docker..."
    report_progress "docker_install" "in_progress" 15 "Installing Docker"

    # Detect OS
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        if [[ "$ID" != "ubuntu" ]]; then
            handle_error "docker_install" "Unsupported OS: $ID"
        fi
    else
        handle_error "docker_install" "Unable to detect OS"
    fi

    # Uninstall conflicting packages
    echo "🔄 Uninstalling conflicting packages..."
    sudo apt-get remove -y docker docker-engine docker.io containerd runc || echo "ℹ️ No conflicting packages found."

    # Set up Docker's apt repository
    echo "📦 Setting up Docker's apt repository..."
    sudo apt-get update
    sudo apt-get install -y ca-certificates curl gnupg lsb-release

    # Add Docker's official GPG key
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

    # Set up the repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

    # Install Docker Engine
    echo "🔄 Installing Docker Engine..."
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker

    # Add current user to docker group
    sudo usermod -aG docker $USER

    echo "✅ Docker installed successfully!"
    docker --version
    report_progress "docker_install" "completed" 40 "Docker installed successfully"
fi

# Clone or update the repository
echo "📥 Setting up Kumulus provider repository..."
report_progress "repo_setup" "in_progress" 50 "Setting up repository"

if [ -d "$INSTALL_DIR" ]; then
    echo "📁 Directory $INSTALL_DIR already exists. Updating..."
    cd "$INSTALL_DIR"
    git pull origin main || handle_error "repo_setup" "Failed to update repository"
else
    echo "📥 Cloning repository..."
    sudo git clone "$REPO_URL" "$INSTALL_DIR" || handle_error "repo_setup" "Failed to clone repository"
    sudo chown -R $(logname):$(logname) "$INSTALL_DIR"
    cd "$INSTALL_DIR"
fi

report_progress "repo_setup" "completed" 60 "Repository setup complete"

# Install Deno if not already installed
echo "🦕 Checking Deno installation..."
report_progress "deno_install" "in_progress" 70 "Installing Deno"

if ! command -v deno &>/dev/null; then
    echo "🔄 Installing Deno..."
    curl -fsSL https://deno.land/x/install/install.sh | sh
    export DENO_INSTALL="${DENO_INSTALL:-$HOME/.deno}"
    export PATH="$DENO_INSTALL/bin:$PATH"
    echo "✅ Deno installed successfully: $(deno --version)"
    report_progress "deno_install" "completed" 80 "Deno installed successfully"
else
    echo "✅ Deno is already installed: $(deno --version)"
    report_progress "deno_install" "completed" 80 "Deno already available"
fi

# Set up environment variables for the agent
echo "⚙️ Configuring environment..."
report_progress "config_setup" "in_progress" 85 "Configuring environment"

# Create environment file for the agent
sudo tee "$KUMULUS_CONFIG/agent.env" > /dev/null << EOF
PROVIDER_ID=${PROVIDER_ID}
KUMULUS_API_URL=${KUMULUS_API_URL}
PROVIDER_TOKEN=${PROVIDER_TOKEN}
RESOURCE_ID=${RESOURCE_ID}
HEARTBEAT_INTERVAL=30000
LOG_LEVEL=info
HEALTH_CHECK_INTERVAL=5
EOF

# Set secure permissions on environment file
sudo chown "$KUMULUS_USER:$KUMULUS_USER" "$KUMULUS_CONFIG/agent.env"
sudo chmod 600 "$KUMULUS_CONFIG/agent.env"

echo "✅ Environment configured"
report_progress "config_setup" "completed" 88 "Environment configured"

# Create systemd service for production deployment
create_systemd_service() {
    echo "🔧 Creating systemd service..."
    report_progress "service_setup" "in_progress" 90 "Creating systemd service"

    sudo tee "/etc/systemd/system/$SERVICE_NAME.service" > /dev/null << EOF
[Unit]
Description=Kumulus Flare Agent
Documentation=https://docs.kumulus.cloud
After=network.target docker.service
Requires=docker.service

[Service]
Type=simple
User=$KUMULUS_USER
Group=$KUMULUS_USER
WorkingDirectory=$INSTALL_DIR
EnvironmentFile=$KUMULUS_CONFIG/agent.env
ExecStart=/usr/bin/deno run --allow-all main.ts
Restart=always
RestartSec=10
TimeoutStartSec=300
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$KUMULUS_CONFIG $KUMULUS_LOGS

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable service
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"

    echo "✅ Systemd service created and enabled"
    report_progress "service_setup" "completed" 92 "Systemd service configured"
}

# Create systemd service
create_systemd_service

# Start the provider agent
echo "🚀 Starting the Kumulus Flare Agent..."
report_progress "agent_start" "in_progress" 95 "Starting Kumulus Flare Agent"

# Start the systemd service
sudo systemctl start "$SERVICE_NAME"

# Wait a moment for agent to start
sleep 5

# Check if agent is running
if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
    report_progress "agent_start" "completed" 100 "Kumulus Flare Agent started successfully"
    echo "🎉 Setup complete! The provider is now running."
    echo ""
    echo "📊 Service Status: $(sudo systemctl is-active $SERVICE_NAME)"
    echo "📊 You can monitor your provider status at: ${KUMULUS_API_URL}/dashboard"
    echo "🔧 Agent logs: sudo journalctl -u $SERVICE_NAME -f"
    echo "🔧 Installation logs: /var/log/docker_install.log"
    echo ""
    echo "🔧 Service Management:"
    echo "   Start:   sudo systemctl start $SERVICE_NAME"
    echo "   Stop:    sudo systemctl stop $SERVICE_NAME"
    echo "   Restart: sudo systemctl restart $SERVICE_NAME"
    echo "   Status:  sudo systemctl status $SERVICE_NAME"
    echo ""
    echo "✅ The agent will automatically start on system boot."
    echo "✅ The agent is now sending heartbeats to the console."

    # Show final status
    echo ""
    echo "🔍 Final Status Check:"
    sudo systemctl status "$SERVICE_NAME" --no-pager -l
else
    handle_error "agent_start" "Agent failed to start properly. Check logs: sudo journalctl -u $SERVICE_NAME"
fi
